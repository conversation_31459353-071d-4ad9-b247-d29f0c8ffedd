<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?attr/selectableItemBackground">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="2dp"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- 使用1:1的宽高比例 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/image_container"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintTop_toTopOf="parent">

                <!-- 图片缩略图 -->
                <ImageView
                    android:id="@+id/file_thumbnail"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/white"
                    android:scaleType="centerCrop"
                    tools:src="@drawable/ic_image_file" />

                <!-- 图标（当没有缩略图时显示） -->
                <ImageView
                    android:id="@+id/file_icon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_image_file"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- 选中状态指示器 -->
                <CheckBox
                    android:id="@+id/checkbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="4dp"
                    android:button="@drawable/checkbox"
                    android:clickable="false"
                    android:gravity="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 文件信息 -->
            <TextView
                android:id="@+id/file_size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#80000000"
                android:gravity="center"
                android:padding="4dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@id/image_container"
                tools:text="244.2KB" />

            <!-- 隐藏文件名，因为在网格视图中不需要显示 -->
            <TextView
                android:id="@+id/file_name"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</FrameLayout>
