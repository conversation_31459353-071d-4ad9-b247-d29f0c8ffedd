<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.MainActivity">

    <!-- Top App Bar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@android:color/transparent"
        android:elevation="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:titleTextColor="@color/toolbar_title_color">

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textColor="@color/toolbar_title_color"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/btn_settings"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="end"
            android:layout_marginEnd="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@mipmap/ic_setting" />
    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="16dp"
            android:paddingBottom="50dp">

            <!-- Progress Indicator Section -->
            <FrameLayout
                android:id="@+id/progress_container"
                android:layout_width="280dp"
                android:layout_height="280dp"
                android:layout_marginTop="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/progress_background"
                    android:layout_width="180dp"
                    android:layout_height="180dp"
                    android:layout_gravity="center"
                    android:src="@drawable/progress_background" />

                <!-- 进度轨道已经在CustomDrawView中绘制，这里不需要额外的View -->

                <com.mobile.app.zencleaner.ui.widgets.CustomDrawView
                    android:id="@+id/progress_arc"
                    android:layout_width="280dp"
                    android:layout_height="280dp"
                    android:layout_gravity="center" />

                <View
                    android:id="@+id/progress_cap"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center"
                    android:background="@drawable/progress_cap"
                    android:elevation="4dp"
                    tools:visibility="visible" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_used_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:alpha="0.8"
                        android:text="Used"
                        android:textColor="#9E9E9E"
                        android:textSize="16sp"
                        android:textStyle="normal"
                        android:visibility="gone" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="bottom"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_storage_percentage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="sans-serif-light"
                            android:text="0"
                            android:textColor="@color/colorPrimary"
                            android:textSize="72sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="2dp"
                            android:text="%"
                            android:textColor="@color/colorPrimary"
                            android:textSize="24sp"
                            android:textStyle="normal" />
                    </LinearLayout>
                </LinearLayout>
            </FrameLayout>

            <TextView
                android:id="@+id/tv_storage_used"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/block_shape"
                android:drawablePadding="5dp"
                android:text="46GB / 103GB"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/progress_container" />

            <!-- Optimize Button -->
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_scan_now"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginHorizontal="30dp"
                android:layout_marginTop="30dp"
                android:background="@drawable/button_primary"
                android:text="@string/scan_now"
                android:textAllCaps="false"
                android:textColor="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/progress_container" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btn_scan_now">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!--                    Junk Removal Card -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="12dp">

                        <LinearLayout
                            android:id="@+id/card_junk"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@drawable/ic_junk" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:layout_weight="1"
                                android:text="@string/junk_removal"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="normal" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/button_secondary"
                                android:paddingHorizontal="12dp"
                                android:paddingVertical="6dp"
                                android:text="@string/manage"
                                android:textColor="@color/colorPrimary"
                                android:visibility="gone" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="12dp">
                        <!--                    Images Card -->
                        <LinearLayout
                            android:id="@+id/card_images"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@drawable/ic_image" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:layout_weight="1"
                                android:text="@string/image_files"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="normal" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/button_secondary"
                                android:paddingHorizontal="12dp"
                                android:paddingVertical="6dp"
                                android:text="@string/manage"
                                android:textColor="@color/colorPrimary"
                                android:visibility="gone" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="12dp">
                        <!--                    Videos Card -->
                        <LinearLayout
                            android:id="@+id/card_videos"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@drawable/ic_video" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:layout_weight="1"
                                android:text="@string/video_files"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="normal" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/button_secondary"
                                android:paddingHorizontal="12dp"
                                android:paddingVertical="6dp"
                                android:text="@string/manage"
                                android:textColor="@color/colorPrimary"
                                android:visibility="gone" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="12dp">
                        <!--                    Large Files Card -->
                        <LinearLayout
                            android:id="@+id/card_large_files"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@drawable/ic_large_files" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:layout_weight="1"
                                android:text="@string/large_files"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="normal" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/button_secondary"
                                android:paddingHorizontal="12dp"
                                android:paddingVertical="6dp"
                                android:text="@string/manage"
                                android:textColor="@color/colorPrimary"
                                android:visibility="gone" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
