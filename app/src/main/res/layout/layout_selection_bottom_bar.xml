<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/button_primary"
    android:elevation="8dp"
    android:layout_marginHorizontal="16dp"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp">

    <LinearLayout
        android:id="@+id/btn_select_all"
        android:layout_width="105dp"
        android:layout_height="48dp"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="All"
            android:textColor="@color/white" />

        <CheckBox
            android:id="@+id/checkbox_select_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:button="@drawable/checkbox" />
    </LinearLayout>

    <Space
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

    <Button
        android:id="@+id/btn_remove"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_weight="1"
        android:background="@color/transparent"
        android:enabled="false"
        android:text="Remove"
        android:textAllCaps="false"
        android:textColor="@color/white" />

    <TextView
        android:id="@+id/tv_selected_size"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        android:visibility="gone"
        tools:text="(25.4 MB)" />
</LinearLayout>
