<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fitsSystemWindows="true"
    tools:context=".ui.activity.JunkScanActivity">

    <!-- Top App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@android:color/transparent"
            app:navigationIcon="@mipmap/ic_back"
            app:title="Junk Removal"
            app:titleTextColor="@color/toolbar_title_color" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- Scanning Animation Section -->
            <FrameLayout
                android:id="@+id/scanning_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="16dp"
                android:paddingTop="20dp"
                android:paddingEnd="16dp"
                android:paddingBottom="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <FrameLayout
                        android:layout_width="200dp"
                        android:layout_height="200dp">

                        <!-- Background Glow Image -->
                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:src="@drawable/clean_progress_background" />

                        <!-- Lottie Animation View -->
                        <com.airbnb.lottie.LottieAnimationView
                            android:id="@+id/lottie_animation"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            app:lottie_autoPlay="true"
                            app:lottie_loop="true"
                            app:lottie_rawRes="@raw/clean" />

                        <!-- Percentage Text -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="bottom"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_scan_percentage"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textColor="@color/text_primary"
                                android:textSize="40sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="2dp"
                                android:layout_marginBottom="4dp"
                                android:text="%"
                                android:textColor="@color/text_primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </FrameLayout>

                    <Space
                        android:layout_width="match_parent"
                        android:layout_height="16dp" />

                    <!-- Current Path Text -->
                    <TextView
                        android:id="@+id/tv_current_path"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:gravity="center"
                        android:maxLines="2"
                        android:paddingHorizontal="16dp"
                        android:text="Initializing..."
                        android:textAlignment="center"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />
                </LinearLayout>
            </FrameLayout>

            <!-- Scan Status Header (Shown after scan completes) -->
            <LinearLayout
                android:id="@+id/scan_status_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:minHeight="200dp"
                android:orientation="vertical"
                android:paddingStart="16dp"
                android:paddingTop="20dp"
                android:paddingEnd="16dp"
                android:paddingBottom="10dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <!-- Total Junk Display -->
                <FrameLayout
                    android:layout_width="200dp"
                    android:layout_height="200dp"
                    android:layout_gravity="center">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/clean_progress_background" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="bottom"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_junk_size_number"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@color/primary_blue"
                            android:textSize="48sp"
                            android:textStyle="bold" />

                        <Space
                            android:layout_width="4dp"
                            android:layout_height="0dp" />

                        <TextView
                            android:id="@+id/tv_junk_size_unit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:text="MB"
                            android:textColor="@color/primary_blue"
                            android:textSize="20sp"
                            android:textStyle="normal" />
                    </LinearLayout>
                </FrameLayout>

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="8dp" />

                <TextView
                    android:id="@+id/tv_scan_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="30dp"
                    android:text="Scan complete. Select items below if needed."
                    android:textAlignment="center"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="10dp" />
            </LinearLayout>

            <!-- Junk Categories List -->

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_junk_categories"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingHorizontal="16dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toTopOf="@id/button_container"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/scan_status_header"
                tools:itemCount="3"
                tools:listitem="@layout/item_junk_category" />

            <!-- Button Container -->
            <FrameLayout
                android:id="@+id/button_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/background"
                android:padding="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <androidx.cardview.widget.CardView
                    android:id="@+id/btn_next_step"
                    android:layout_width="match_parent"
                    android:layout_height="52dp"
                    app:cardCornerRadius="26dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/button_primary"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Next step"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </FrameLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>