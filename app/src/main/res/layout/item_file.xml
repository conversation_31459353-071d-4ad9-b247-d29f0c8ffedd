<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp">

    <FrameLayout
        android:id="@+id/thumbnail_container"
        android:layout_width="56dp"
        android:layout_height="56dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/file_thumbnail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="File thumbnail"
            android:scaleType="centerCrop"
            android:visibility="gone"
            tools:src="@tools:sample/backgrounds/scenic"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/file_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:contentDescription="File icon"
            tools:src="@drawable/ic_document_file" />
    </FrameLayout>

    <TextView
        android:id="@+id/file_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="middle"
        android:singleLine="true"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        app:layout_constraintEnd_toStartOf="@+id/checkbox"
        app:layout_constraintStart_toEndOf="@+id/thumbnail_container"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="example_video_file.mp4" />

    <TextView
        android:id="@+id/file_size"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="4dp"
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        app:layout_constraintStart_toEndOf="@+id/thumbnail_container"
        app:layout_constraintTop_toBottomOf="@+id/file_name"
        tools:text="25.4 MB" />

    <CheckBox
        android:id="@+id/checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@drawable/checkbox"
        android:checked="false"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
