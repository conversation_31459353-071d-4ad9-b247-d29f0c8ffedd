<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/white"
    android:layout_marginBottom="10dp"
    app:cardCornerRadius="10dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="4dp"
        android:paddingVertical="12dp">

        <!-- Icon Background -->
        <FrameLayout
            android:id="@+id/icon_container"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_category_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                app:tint="@color/colorPrimary"
                tools:src="@android:drawable/ic_menu_delete" />
        </FrameLayout>

        <!-- Category Name and Description -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_size"
            app:layout_constraintStart_toEndOf="@id/icon_container"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_category_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_primary"
                android:textSize="15sp"
                android:textStyle="normal"
                tools:text="App Cache" />

            <TextView
                android:id="@+id/tv_category_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="Scanning..." />
        </LinearLayout>

        <!-- Size Text -->
        <TextView
            android:id="@+id/tv_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:textStyle="normal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_status"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="55.6 MB" />

        <!-- Status Icon -->
        <ImageView
            android:id="@+id/iv_status"
            android:layout_width="24dp"
            android:layout_height="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@mipmap/ic_checkbox_checked_green" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
