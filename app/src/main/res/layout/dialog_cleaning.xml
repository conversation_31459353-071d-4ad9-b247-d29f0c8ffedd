<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:id="@+id/dialog_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Cleaning"
            android:textColor="@color/text_primary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottie_animation"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/clean" />

        <TextView
            android:id="@+id/dialog_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:text="Cleaning junk files..."
            android:textColor="@color/text_secondary"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/dialog_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="0%"
            android:textColor="@color/primary_blue"
            android:textSize="18sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/dialog_path"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="middle"
            android:gravity="center"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:background="@android:color/transparent"
            android:text="Cancel"
            android:visibility="gone"
            android:textAllCaps="false"
            android:textColor="@color/primary_blue" />

    </LinearLayout>
</androidx.cardview.widget.CardView>
