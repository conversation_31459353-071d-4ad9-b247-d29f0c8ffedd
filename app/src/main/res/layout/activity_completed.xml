<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.CompletedActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/white"
        app:navigationIcon="@mipmap/ic_back"
        app:title="@string/cleanup_completed"
        app:titleTextColor="@color/toolbar_title_color" />


    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <!-- Spacer -->
            <Space
                android:layout_width="match_parent"
                android:layout_height="32dp" />

            <!-- Completed Image -->
            <ImageView
                android:id="@+id/completed_image"
                android:layout_width="210dp"
                android:layout_height="210dp"
                android:contentDescription="Completed"
                android:src="@mipmap/bg_completed" />

            <!-- Spacer -->
            <Space
                android:layout_width="match_parent"
                android:layout_height="32dp" />

            <!-- Status Text -->
            <TextView
                android:id="@+id/status_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingHorizontal="20dp"
                android:text="The current mobile phone statushas reached the best!"
                android:textColor="#37405D"
                android:textSize="16sp" />

            <!-- Spacer -->
            <Space
                android:layout_width="match_parent"
                android:layout_height="40dp" />

            <!-- Category List Container -->
            <LinearLayout
                android:id="@+id/category_list_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- Junk Files Category -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="12dp">

                        <LinearLayout
                            android:id="@+id/junk_files_category"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:contentDescription="Junk Files"
                                android:src="@drawable/ic_junk" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:layout_weight="1"
                                android:text="@string/junk_removal"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>


                    <!-- Images Category -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="12dp">

                        <LinearLayout
                            android:id="@+id/images_category"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:contentDescription="Images"
                                android:src="@drawable/ic_image" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:layout_weight="1"
                                android:text="@string/image_files"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <!-- Videos Category -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="12dp">

                        <LinearLayout
                            android:id="@+id/videos_category"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:src="@drawable/ic_video" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:layout_weight="1"
                                android:text="@string/video_files"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <!-- Large Files Category -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="12dp">

                        <LinearLayout
                            android:id="@+id/large_files_category"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:src="@drawable/ic_large_files" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:layout_weight="1"
                                android:text="@string/large_files"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>