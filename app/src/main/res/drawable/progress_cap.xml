<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 外层白色圆圈 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/white" />
            <size android:width="30dp" android:height="30dp" />
            <!-- 添加阴影效果 -->
            <stroke android:color="#10000000" android:width="0.5dp" />
        </shape>
    </item>
    <!-- 内层深蓝色圆圈，与设计图一致 -->
    <item android:left="10dp" android:top="10dp" android:right="10dp" android:bottom="10dp">
        <shape android:shape="oval">
            <solid android:color="#3F51B5" /> <!-- 深蓝色，与设计图一致 -->
        </shape>
    </item>
</layer-list>
