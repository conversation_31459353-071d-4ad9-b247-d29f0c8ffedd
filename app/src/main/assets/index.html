<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Links</title>
    <style>
        body {
            font-family: sans-serif;
            margin: 0;
            padding: 15px;
            background-color: #FFFFFF;
        }

        /* Container for search input and icon */
        .search-container {
            position: relative; /* Needed for absolute positioning of the icon */
            margin-bottom: 25px;
        }

        /* Style for the search icon */
        .search-icon {
            position: absolute; /* Position the icon relative to the container */
            left: 12px;        /* Distance from the left edge of the container */
            top: 50%;          /* Position top edge at the middle */
            transform: translateY(-50%); /* Shift icon up by half its height for perfect vertical centering */
            width: 20px;       /* Adjust icon size */
            height: 20px;      /* Adjust icon size */
            color: #B2F100;
            opacity: 0.6;      /* Make icon slightly less prominent (optional) */
            pointer-events: none; /* Prevent icon from interfering with clicks (optional but good practice) */
        }

        #search-input {
            width: 100%;
            padding: 10px 15px 10px 40px; /* Top, Right, Bottom, LEFT padding */
            font-size: 16px;
            border: 1px solid #fff; /* White border */
            border-radius: 16px;
            box-sizing: border-box;
            color: #F4F6FA;     /* --- MODIFIED: Makes the typed text white --- */
            background-color: #F4F6FA; /* Keep background transparent */
            /* Optional: Keep caret color visible */
            caret-color: rgba(255, 255, 255, 0.8); /* Slightly more opaque white cursor */
        }

        /* --- Focus state --- (Keep border white as previously requested) */
         #search-input:focus {
             outline: none;
             border-color: #fff;
         }

        /* --- Placeholder style --- (Keep it distinct, e.g., semi-transparent white) */
        #search-input::placeholder {
             color: #37405D; /* Semi-transparent white placeholder */
             opacity: 1;
        }

        /* --- Icon Grid Styles (remain the same) --- */
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            justify-items: center;
            align-items: center;
        }
        .icon-grid a {
            display: block;
            text-align: center;
            text-decoration: none;
        }
        .icon-grid img {
            width: 50px;
            height: 50px;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

    </style>
</head>
<body>

<!-- Updated Search Container -->
<div class="search-container">
    <!-- Add the icon image BEFORE the input -->
    <img src="images/ic_search.png" alt="Search" class="search-icon">
    <!-- The input field -->
    <input type="text" id="search-input" placeholder="Search">
</div>
<!-- End Updated Search Container -->


<div class="icon-grid">
    <!-- Row 1 -->
    <a href="https://www.google.com" target="_blank"><img src="images/google.png" alt="Google"></a>
    <a href="https://www.youtube.com" target="_blank"><img src="images/youtube.png" alt="YouTube"></a>
    <a href="https://www.facebook.com" target="_blank"><img src="images/facebook.png" alt="Facebook"></a>
    <a href="https://www.amazon.com" target="_blank"><img src="images/amazon.png" alt="Amazon"></a>

    <!-- Row 2 -->
    <a href="https://www.wikipedia.org" target="_blank"><img src="images/wikipedia.png" alt="Wikipedia"></a>
    <a href="https://www.twitter.com" target="_blank"><img src="images/twitter.png" alt="Twitter"></a>
    <a href="https://www.instagram.com" target="_blank"><img src="images/instagram.png" alt="Instagram"></a>
    <a href="https://www.reddit.com" target="_blank"><img src="images/reddit.png" alt="Reddit"></a>
</div>

<script>
    const searchInput = document.getElementById('search-input');

    searchInput.addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            const searchTerm = searchInput.value.trim();
            if (searchTerm) {
                const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(searchTerm)}`;
                window.location.href = searchUrl; // Navigate within the WebView or trigger external browser based on WebViewClient
            }
        }
    });
</script>

</body>
</html>