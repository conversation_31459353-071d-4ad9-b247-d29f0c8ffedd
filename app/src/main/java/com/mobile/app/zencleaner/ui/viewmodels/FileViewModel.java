package com.mobile.app.zencleaner.ui.viewmodels;

import android.app.Application;
import android.app.PendingIntent;
import android.app.RecoverableSecurityException;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.IntentSender;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.provider.MediaStore;
import android.util.Log;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.IntentSenderRequest;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.mobile.app.zencleaner.models.SelectableFile;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

public class FileViewModel extends AndroidViewModel {
    private static final String TAG = "MediaViewModel";

    // 文件大小阈值：默认为0，表示不过滤
    private static final long DEFAULT_FILE_SIZE_THRESHOLD = 0L;

    // 媒体类型
    public static final int MEDIA_TYPE_ALL = 0;
    public static final int MEDIA_TYPE_IMAGE = 1;
    public static final int MEDIA_TYPE_VIDEO = 2;
    public static final int MEDIA_TYPE_AUDIO = 3;
    public static final int MEDIA_TYPE_LARGE_FILES = 4;

    private final MutableLiveData<List<SelectableFile>> files = new MutableLiveData<>(new ArrayList<>());
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    private final MutableLiveData<String> error = new MutableLiveData<>(null);
    private final MutableLiveData<Boolean> permissionGranted = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> showPermissionDialog = new MutableLiveData<>(false);

    private final List<SelectableFile> selectedFiles = new ArrayList<>();
    private final Executor executor = Executors.newSingleThreadExecutor();
    private final ContentResolver contentResolver;

    private int mediaType = MEDIA_TYPE_ALL;
    private long fileSizeThreshold = DEFAULT_FILE_SIZE_THRESHOLD;

    public FileViewModel(@NonNull Application application) {
        super(application);
        contentResolver = application.getContentResolver();
    }

    // 设置媒体类型和大小阈值
    public void setMediaTypeAndThreshold(int mediaType, long fileSizeThreshold) {
        this.mediaType = mediaType;
        this.fileSizeThreshold = fileSizeThreshold;
    }

    // Getters for LiveData
    public LiveData<List<SelectableFile>> getFiles() {
        return files;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getError() {
        return error;
    }

    public LiveData<Boolean> getPermissionGranted() {
        return permissionGranted;
    }

    public LiveData<Boolean> getShowPermissionDialog() {
        return showPermissionDialog;
    }

    public List<SelectableFile> getSelectedFiles() {
        return selectedFiles;
    }

    // Permission handling
    public void onPermissionResult(boolean granted) {
        Log.d(TAG, "Permission result received: " + granted);
        permissionGranted.setValue(granted);
        if (granted) {
            loadFiles();
        } else {
            error.setValue("Storage permission denied.");
        }
    }

    public void requestPermission() {
        showPermissionDialog.setValue(true);
    }

    public void dismissPermissionDialog() {
        showPermissionDialog.setValue(false);
    }

    // File loading
    public void loadFiles() {
        if (!permissionGranted.getValue()) {
            Log.d(TAG, "loadFiles called but permission not granted.");
            error.setValue("Permission required.");
            return;
        }

        if (isLoading.getValue()) {
            Log.d(TAG, "loadFiles called but already loading.");
            return;
        }

        isLoading.setValue(true);
        error.setValue(null);

        executor.execute(() -> {
            try {
                List<SelectableFile> fileList = queryMediaFiles();
                files.postValue(fileList);
                isLoading.postValue(false);
                selectedFiles.clear();
            } catch (Exception e) {
                Log.e(TAG, "Error loading files: " + e.getMessage(), e);
                error.postValue("Failed to load files: " + e.getMessage());
                isLoading.postValue(false);
            }
        });
    }

    private List<SelectableFile> queryMediaFiles() {
        List<SelectableFile> fileList = new ArrayList<>();

        Log.d(TAG, "Querying media files with type: " + mediaType + ", threshold: " + fileSizeThreshold);

        try {
            // 使用 MediaStore 查询文件
            String[] projection = {
                    MediaStore.Files.FileColumns._ID,
                    MediaStore.Files.FileColumns.DISPLAY_NAME,
                    MediaStore.Files.FileColumns.DATA,
                    MediaStore.Files.FileColumns.SIZE,
                    MediaStore.Files.FileColumns.MIME_TYPE,
                    MediaStore.Files.FileColumns.MEDIA_TYPE
            };

            // 构建查询条件
            String selection;
            String[] selectionArgs;

            if (fileSizeThreshold > 0) {
                // 如果有大小阈值，添加大小条件
                selection = MediaStore.Files.FileColumns.SIZE + " > ?";
                selectionArgs = new String[]{String.valueOf(fileSizeThreshold)};
            } else {
                selection = null;
                selectionArgs = null;
            }

            // 根据媒体类型选择不同的URI和查询条件
            Uri uri;

            // 使用通用的Files URI来查询所有类型的文件
            uri = MediaStore.Files.getContentUri("external");

            switch (mediaType) {
                case MEDIA_TYPE_IMAGE:
                    // 对于图片，添加MIME类型条件
                    selection = (selection != null && !selection.isEmpty() ? selection + " AND " : "") +
                            MediaStore.Files.FileColumns.MIME_TYPE + " LIKE ?";
                    selectionArgs = new String[]{"image/%"};
                    break;
                case MEDIA_TYPE_VIDEO:
                    // 对于视频，添加MIME类型条件
                    selection = (selection != null && !selection.isEmpty() ? selection + " AND " : "") +
                            MediaStore.Files.FileColumns.MIME_TYPE + " LIKE ?";
                    selectionArgs = new String[]{"video/%"};
                    break;
                case MEDIA_TYPE_AUDIO:
                    // 对于音频，添加MIME类型条件
                    selection = (selection != null && !selection.isEmpty() ? selection + " AND " : "") +
                            MediaStore.Files.FileColumns.MIME_TYPE + " LIKE ?";
                    selectionArgs = new String[]{"audio/%"};
                    break;
                case MEDIA_TYPE_LARGE_FILES:
                    // 对于大文件，我们需要添加媒体类型条件
                    String mediaTypeSelection = "(" + MediaStore.Files.FileColumns.MIME_TYPE + " LIKE ? OR " +
                            MediaStore.Files.FileColumns.MIME_TYPE + " LIKE ? OR " +
                            MediaStore.Files.FileColumns.MIME_TYPE + " LIKE ?)";
                    String sizeSelection = MediaStore.Files.FileColumns.SIZE + " > ?";
                    selection = mediaTypeSelection + " AND " + sizeSelection;
                    selectionArgs = new String[]{
                            "image/%",
                            "video/%",
                            "audio/%",
                            String.valueOf(fileSizeThreshold)
                    };
                    break;
                default:
                    // 不添加特定条件，查询所有文件
                    break;
            }

            String sortOrder = MediaStore.Files.FileColumns.SIZE + " DESC";

            Log.d(TAG, "Query URI: " + uri + ", selection: " + selection);
            if (selectionArgs != null) {
                for (int i = 0; i < selectionArgs.length; i++) {
                    Log.d(TAG, "Selection arg " + i + ": " + selectionArgs[i]);
                }
            }

            try (Cursor cursor = contentResolver.query(uri, projection, selection, selectionArgs, sortOrder)) {
                if (cursor != null) {
                    Log.d(TAG, "Cursor count: " + cursor.getCount());
                    int idColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID);
                    int nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DISPLAY_NAME);
                    int pathColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATA);
                    int sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.SIZE);
                    int mimeTypeColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.MIME_TYPE);
                    int mediaTypeColumn = cursor.getColumnIndex(MediaStore.Files.FileColumns.MEDIA_TYPE);

                    while (cursor.moveToNext()) {
                        long id = cursor.getLong(idColumn);
                        String name = cursor.getString(nameColumn);
                        String path = cursor.getString(pathColumn);
                        long size = cursor.getLong(sizeColumn);
                        String mimeType = cursor.getString(mimeTypeColumn);
                        int fileMediaType = mediaTypeColumn != -1 ? cursor.getInt(mediaTypeColumn) : 0;

                        // 根据媒体类型构建正确的URI
                        Uri contentUri;
                        switch (mediaType) {
                            case MEDIA_TYPE_IMAGE:
                                contentUri = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id);
                                break;
                            case MEDIA_TYPE_VIDEO:
                                contentUri = ContentUris.withAppendedId(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, id);
                                break;
                            case MEDIA_TYPE_AUDIO:
                                contentUri = ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, id);
                                break;
                            default:
                                if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE) {
                                    contentUri = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id);
                                } else if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO) {
                                    contentUri = ContentUris.withAppendedId(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, id);
                                } else if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_AUDIO) {
                                    contentUri = ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, id);
                                } else {
                                    contentUri = ContentUris.withAppendedId(uri, id);
                                }
                                break;
                        }

                        // 检查文件是否存在
                        File file = new File(path);
                        if (file.exists() && file.isFile() && (fileSizeThreshold <= 0 || size > fileSizeThreshold)) {
                            fileList.add(new SelectableFile(
                                    id,
                                    name,
                                    path,
                                    contentUri.toString(),
                                    size,
                                    mimeType,
                                    fileMediaType
                            ));
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error querying media files: " + e.getMessage(), e);
        }

        Log.d(TAG, "Query result: found " + fileList.size() + " files");
        return fileList;
    }

    // Selection handling
    public void toggleItemSelection(SelectableFile file) {
        boolean newState = !file.isSelected();
        file.setSelected(newState);

        if (newState) {
            if (!selectedFiles.contains(file)) {
                selectedFiles.add(file);
            }
        } else {
            selectedFiles.remove(file);
        }

        // 记录日志
        Log.d(TAG, "Toggle item selection: " + file.getName() + ", selected: " + newState + ", total selected: " + selectedFiles.size());
    }

    public void toggleSelectAll() {
        List<SelectableFile> currentFiles = files.getValue();
        if (currentFiles == null || currentFiles.isEmpty()) {
            return;
        }

        boolean areAllSelected = selectedFiles.size() == currentFiles.size();
        boolean targetState = !areAllSelected;

        // 清空当前选中的文件
        selectedFiles.clear();

        // 更新所有文件的选中状态
        for (SelectableFile file : currentFiles) {
            file.setSelected(targetState);
            if (targetState) {
                selectedFiles.add(file);
            }
        }

        // 记录日志
        Log.d(TAG, "Toggle select all: " + targetState + ", selected files: " + selectedFiles.size());
    }

    // Deletion handling
    public void deleteSelectedFiles(ActivityResultLauncher<IntentSenderRequest> launcher) {
        if (selectedFiles.isEmpty()) {
            return;
        }

        executor.execute(() -> {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    deleteFilesApi30(launcher);
                } else if (Build.VERSION.SDK_INT == Build.VERSION_CODES.Q) {
                    deleteFilesApi29(launcher);
                } else {
                    deleteFilesLegacy();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error deleting files: " + e.getMessage(), e);
                error.postValue("Failed to delete files: " + e.getMessage());
            }
        });
    }

    private void deleteFilesApi30(ActivityResultLauncher<IntentSenderRequest> launcher) {
        if (launcher == null) {
            error.postValue("Cannot delete files (internal error).");
            return;
        }

        try {
            // 按媒体类型分组文件
            List<Uri> imageUris = new ArrayList<>();
            List<Uri> videoUris = new ArrayList<>();
            List<Uri> audioUris = new ArrayList<>();
            List<Uri> documentUris = new ArrayList<>();

            for (SelectableFile file : selectedFiles) {
                int fileMediaType = file.getMediaType();
                String mimeType = file.getMimeType();

                // 获取正确的媒体URI
                Uri uri = getMediaStoreUri(file);
                Log.d(TAG, "File: " + file.getName() + ", Original URI: " + file.getUri() + ", Corrected URI: " + uri);

                // 根据媒体类型分组
                if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE ||
                        (mimeType != null && mimeType.startsWith("image/"))) {
                    imageUris.add(uri);
                    Log.d(TAG, "Added to imageUris: " + file.getName());
                } else if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO ||
                        (mimeType != null && mimeType.startsWith("video/"))) {
                    videoUris.add(uri);
                    Log.d(TAG, "Added to videoUris: " + file.getName());
                } else if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_AUDIO ||
                        (mimeType != null && mimeType.startsWith("audio/"))) {
                    audioUris.add(uri);
                    Log.d(TAG, "Added to audioUris: " + file.getName());
                } else {
                    documentUris.add(uri);
                    Log.d(TAG, "Added to documentUris: " + file.getName());
                }
            }

            // 处理图片文件
            if (!imageUris.isEmpty()) {
                try {
                    Log.d(TAG, "Creating delete request for " + imageUris.size() + " image files");
                    PendingIntent pendingIntent = null;
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        pendingIntent = MediaStore.createDeleteRequest(contentResolver, imageUris);
                    }
                    IntentSenderRequest request = new IntentSenderRequest.Builder(pendingIntent.getIntentSender()).build();
                    launcher.launch(request);
                    return; // 一次只处理一种类型
                } catch (Exception e) {
                    Log.e(TAG, "Error creating delete request for image files: " + e.getMessage(), e);
                }
            }

            // 如果图片处理失败或没有图片，尝试处理视频文件
            if (!videoUris.isEmpty()) {
                try {
                    Log.d(TAG, "Creating delete request for " + videoUris.size() + " video files");
                    PendingIntent pendingIntent = null;
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        pendingIntent = MediaStore.createDeleteRequest(contentResolver, videoUris);
                    }
                    IntentSenderRequest request = new IntentSenderRequest.Builder(pendingIntent.getIntentSender()).build();
                    launcher.launch(request);
                    return; // 一次只处理一种类型
                } catch (Exception e) {
                    Log.e(TAG, "Error creating delete request for video files: " + e.getMessage(), e);
                }
            }

            // 如果视频处理失败或没有视频，尝试处理音频文件
            if (!audioUris.isEmpty()) {
                try {
                    Log.d(TAG, "Creating delete request for " + audioUris.size() + " audio files");
                    PendingIntent pendingIntent = null;
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        pendingIntent = MediaStore.createDeleteRequest(contentResolver, audioUris);
                    }
                    IntentSenderRequest request = new IntentSenderRequest.Builder(pendingIntent.getIntentSender()).build();
                    launcher.launch(request);
                    return; // 一次只处理一种类型
                } catch (Exception e) {
                    Log.e(TAG, "Error creating delete request for audio files: " + e.getMessage(), e);
                }
            }

            // 处理非媒体文件（文档等）
            if (!documentUris.isEmpty()) {
                Log.w(TAG, "Non-media files cannot be deleted using MediaStore.createDeleteRequest(). " +
                        "These files will be skipped: " + documentUris.size() + " files");
                Toast.makeText(getApplication(), "Some files cannot be deleted using this method", Toast.LENGTH_SHORT).show();
            }

            // 如果没有文件可以删除
            if (imageUris.isEmpty() && videoUris.isEmpty() && audioUris.isEmpty() && documentUris.isEmpty()) {
                Toast.makeText(getApplication(), "No files to delete", Toast.LENGTH_SHORT).show();
            } else if (imageUris.isEmpty() && videoUris.isEmpty() && audioUris.isEmpty() && !documentUris.isEmpty()) {
                Toast.makeText(getApplication(), "Selected files cannot be deleted using this method", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error creating delete request: " + e.getMessage(), e);
            error.postValue("Failed to create delete request: " + e.getMessage());
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    private void deleteFilesApi29(ActivityResultLauncher<IntentSenderRequest> launcher) {
        if (launcher == null) {
            error.postValue("Cannot delete files (internal error).");
            return;
        }

        try {
            // 在 API 29 上，我们需要逐个删除文件并处理异常
            // 按媒体类型分组文件
            List<SelectableFile> imageFiles = new ArrayList<>();
            List<SelectableFile> videoFiles = new ArrayList<>();
            List<SelectableFile> audioFiles = new ArrayList<>();
            List<SelectableFile> documentFiles = new ArrayList<>();

            for (SelectableFile file : selectedFiles) {
                int fileMediaType = file.getMediaType();
                String mimeType = file.getMimeType();

                // 根据媒体类型分组
                if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE ||
                        (mimeType != null && mimeType.startsWith("image/"))) {
                    imageFiles.add(file);
                    Log.d(TAG, "Added to imageFiles: " + file.getName());
                } else if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO ||
                        (mimeType != null && mimeType.startsWith("video/"))) {
                    videoFiles.add(file);
                    Log.d(TAG, "Added to videoFiles: " + file.getName());
                } else if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_AUDIO ||
                        (mimeType != null && mimeType.startsWith("audio/"))) {
                    audioFiles.add(file);
                    Log.d(TAG, "Added to audioFiles: " + file.getName());
                } else {
                    documentFiles.add(file);
                    Log.d(TAG, "Added to documentFiles: " + file.getName());
                }
            }

            // 先处理图片文件
            if (!imageFiles.isEmpty()) {
                Log.d(TAG, "Attempting to delete " + imageFiles.size() + " image files");
                boolean securityExceptionThrown = false;

                for (SelectableFile file : imageFiles) {
                    try {
                        Uri uri = getMediaStoreUri(file);
                        int result = contentResolver.delete(uri, null, null);
                        Log.d(TAG, "Deleted image file: " + file.getName() + ", result: " + result);
                    } catch (RecoverableSecurityException e) {
                        // 捕获安全异常，并请求用户确认
                        IntentSender intentSender = e.getUserAction().getActionIntent().getIntentSender();
                        IntentSenderRequest request = new IntentSenderRequest.Builder(intentSender).build();
                        launcher.launch(request);
                        securityExceptionThrown = true;
                        return; // 一次只处理一个异常
                    } catch (Exception e) {
                        Log.e(TAG, "Error deleting image file: " + file.getName() + ", error: " + e.getMessage());
                    }
                }

                if (securityExceptionThrown) {
                    return;
                }
            }

            // 处理视频文件
            if (!videoFiles.isEmpty()) {
                Log.d(TAG, "Attempting to delete " + videoFiles.size() + " video files");
                boolean securityExceptionThrown = false;

                for (SelectableFile file : videoFiles) {
                    try {
                        Uri uri = getMediaStoreUri(file);
                        int result = contentResolver.delete(uri, null, null);
                        Log.d(TAG, "Deleted video file: " + file.getName() + ", result: " + result);
                    } catch (RecoverableSecurityException e) {
                        // 捕获安全异常，并请求用户确认
                        IntentSender intentSender = e.getUserAction().getActionIntent().getIntentSender();
                        IntentSenderRequest request = new IntentSenderRequest.Builder(intentSender).build();
                        launcher.launch(request);
                        securityExceptionThrown = true;
                        return; // 一次只处理一个异常
                    } catch (Exception e) {
                        Log.e(TAG, "Error deleting video file: " + file.getName() + ", error: " + e.getMessage());
                    }
                }

                if (securityExceptionThrown) {
                    return;
                }
            }

            // 处理音频文件
            if (!audioFiles.isEmpty()) {
                Log.d(TAG, "Attempting to delete " + audioFiles.size() + " audio files");
                boolean securityExceptionThrown = false;

                for (SelectableFile file : audioFiles) {
                    try {
                        Uri uri = getMediaStoreUri(file);
                        int result = contentResolver.delete(uri, null, null);
                        Log.d(TAG, "Deleted audio file: " + file.getName() + ", result: " + result);
                    } catch (RecoverableSecurityException e) {
                        // 捕获安全异常，并请求用户确认
                        IntentSender intentSender = e.getUserAction().getActionIntent().getIntentSender();
                        IntentSenderRequest request = new IntentSenderRequest.Builder(intentSender).build();
                        launcher.launch(request);
                        securityExceptionThrown = true;
                        return; // 一次只处理一个异常
                    } catch (Exception e) {
                        Log.e(TAG, "Error deleting audio file: " + file.getName() + ", error: " + e.getMessage());
                    }
                }

                if (securityExceptionThrown) {
                    return;
                }
            }

            // 处理非媒体文件
            if (!documentFiles.isEmpty()) {
                Log.d(TAG, "Attempting to delete " + documentFiles.size() + " document files");
                boolean securityExceptionThrown = false;

                for (SelectableFile file : documentFiles) {
                    try {
                        Uri uri = getMediaStoreUri(file);
                        int result = contentResolver.delete(uri, null, null);
                        Log.d(TAG, "Deleted document file: " + file.getName() + ", result: " + result);
                    } catch (RecoverableSecurityException e) {
                        // 捕获安全异常，并请求用户确认
                        IntentSender intentSender = e.getUserAction().getActionIntent().getIntentSender();
                        IntentSenderRequest request = new IntentSenderRequest.Builder(intentSender).build();
                        launcher.launch(request);
                        securityExceptionThrown = true;
                        return; // 一次只处理一个异常
                    } catch (Exception e) {
                        Log.e(TAG, "Error deleting document file: " + file.getName() + ", error: " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error deleting files on API 29: " + e.getMessage(), e);
            error.postValue("Failed to delete files: " + e.getMessage());
        }
    }

    private void deleteFilesLegacy() {
        try {
            // 在旧版 Android 上，我们可以直接删除文件
            int successCount = 0;
            int failCount = 0;

            for (SelectableFile file : selectedFiles) {
                try {
                    Uri uri = getMediaStoreUri(file);
                    int result = contentResolver.delete(uri, null, null);
                    if (result > 0) {
                        successCount++;
                    } else {
                        failCount++;
                        Log.w(TAG, "Failed to delete file: " + file.getName() + ", result: " + result);
                    }
                } catch (Exception e) {
                    failCount++;
                    Log.e(TAG, "Error deleting file: " + file.getName() + ", error: " + e.getMessage());
                }
            }

            // 记录删除结果
            Log.i(TAG, "Delete result: " + successCount + " files deleted, " + failCount + " files failed");

            // 删除成功后重新加载文件列表
            loadFiles();

            // 显示结果提示
            if (failCount > 0) {
                Toast.makeText(getApplication(),
                        "Deleted " + successCount + " files. Failed to delete " + failCount + " files.",
                        Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(getApplication(),
                        "Successfully deleted " + successCount + " files.",
                        Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error deleting files (legacy): " + e.getMessage(), e);
            error.postValue("Failed to delete files: " + e.getMessage());
        }
    }

    public void handleActivityResult(int resultCode) {
        // 处理删除操作的结果
        if (resultCode == android.app.Activity.RESULT_OK) {
            // 删除成功，重新加载文件列表
            loadFiles();
        }
    }

    public long getTotalSelectedSize() {
        long total = 0;
        for (SelectableFile file : selectedFiles) {
            total += file.getSizeBytes();
        }
        return total;
    }

    /**
     * 检查URI是否来自媒体存储
     */
    private boolean isMediaStoreUri(Uri uri) {
        if (uri == null) return false;

        String uriString = uri.toString();
        return uriString.startsWith("content://media/") ||
               uriString.startsWith("content://com.android.providers.media.");
    }

    /**
     * 获取正确的媒体URI
     */
    private Uri getMediaStoreUri(SelectableFile file) {
        long id = file.getId();
        int fileMediaType = file.getMediaType();
        String mimeType = file.getMimeType();

        Uri baseUri;
        if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE ||
                (mimeType != null && mimeType.startsWith("image/"))) {
            baseUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
        } else if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO ||
                (mimeType != null && mimeType.startsWith("video/"))) {
            baseUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
        } else if (fileMediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_AUDIO ||
                (mimeType != null && mimeType.startsWith("audio/"))) {
            baseUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
        } else {
            // 如果不是媒体文件，使用原始 URI
            return Uri.parse(file.getUri());
        }

        return ContentUris.withAppendedId(baseUri, id);
    }
}
