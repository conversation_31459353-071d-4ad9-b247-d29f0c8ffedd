package com.mobile.app.zencleaner.ui.activity;

import com.mobile.app.zencleaner.ui.viewmodel.MediaViewModel;

/**
 * 音频文件管理页面
 */
public class AudiosActivity extends FileActivity {

    @Override
    protected int getMediaType() {
        return MediaViewModel.MEDIA_TYPE_AUDIO;
    }

    @Override
    protected long getFileSizeThreshold() {
        return 0; // 不过滤大小
    }

    @Override
    protected void onCreate(android.os.Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        android.util.Log.d("AudiosActivity", "onCreate: MediaType=" + getMediaType());
    }

    @Override
    protected String getToolbarTitle() {
        return "Audios";
    }
}
