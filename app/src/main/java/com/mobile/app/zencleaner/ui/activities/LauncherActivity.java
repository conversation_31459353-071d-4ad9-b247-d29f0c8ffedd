package com.mobile.app.zencleaner.ui.activities;

import android.animation.ObjectAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsControllerCompat;

import com.mobile.app.zencleaner.databinding.ActivityLauncherBinding;

/**
 * Java version of SplashScreen
 * Displays a splash screen with a progress bar
 */
public class LauncherActivity extends AppCompatActivity {

    private ActivityLauncherBinding binding;
    private Handler handler;
    private static final int SPLASH_DELAY = 2000; // 2 seconds

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Use ViewBinding
        binding = ActivityLauncherBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Set up transparent status bar
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        getWindow().setStatusBarColor(android.graphics.Color.TRANSPARENT);
        WindowInsetsControllerCompat windowInsetsController = new WindowInsetsControllerCompat(
                getWindow(), getWindow().getDecorView());
        windowInsetsController.setAppearanceLightStatusBars(true);

        // Initialize views
        ProgressBar progressBar = binding.progressBar;
        TextView loadingText = binding.loadingText;
        loadingText.setText("Loading...");

        // Initialize handler for progress updates
        handler = new Handler(Looper.getMainLooper());

        // Start progress animation
        startProgressAnimation(progressBar);
    }

    private void startProgressAnimation(ProgressBar progressBar) {
        // Create a smooth progress animation
        ObjectAnimator progressAnimator = ObjectAnimator.ofInt(progressBar, "progress", 0, 100);
        progressAnimator.setDuration(1500); // 1.5 seconds total
        progressAnimator.start();

        // After animation completes, navigate to home
        handler.postDelayed(this::navigateToHome, SPLASH_DELAY);
    }

    private void navigateToHome() {
        Intent intent = new Intent(this, HomeActivity.class);
        startActivity(intent);
        finish(); // Close this activity
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Remove any pending callbacks to prevent memory leaks
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        binding = null;
    }
}
