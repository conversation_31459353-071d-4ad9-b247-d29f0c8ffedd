package com.mobile.app.zencleaner.util;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

/**
 * Utility class for file operations
 */
public class Tools {

    private Tools() {
        // Private constructor to prevent instantiation
    }

    /**
     * Format file size in bytes to human-readable format
     * @param bytes Size in bytes
     * @return Formatted size string (e.g., "1.5 MB")
     */
    public static String formatSize(long bytes) {
        if (bytes <= 0) {
            return "0 B";
        }
        
        final String[] units = new String[] { "B", "KB", "MB", "GB", "TB" };
        int digitGroups = (int) (Math.log10(bytes) / Math.log10(1024));
        
        // Limit to available units
        digitGroups = Math.min(digitGroups, units.length - 1);
        
        DecimalFormat df = new DecimalFormat("#,##0.#");
        return df.format(bytes / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
    }

    public static String formatStorageSize(long bytes) {
        if (bytes < 0) return "0 B";

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        double size = bytes;
        int unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        NumberFormat format = NumberFormat.getNumberInstance(Locale.US);
        format.setMaximumFractionDigits(unitIndex < 2 ? 0 : 1);

        return format.format(size) + " " + units[unitIndex];
    }
}
