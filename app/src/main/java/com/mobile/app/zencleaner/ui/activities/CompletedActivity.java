package com.mobile.app.zencleaner.ui.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;

import com.mobile.app.zencleaner.R;
import com.mobile.app.zencleaner.databinding.ActivityCompletedBinding;

/**
 * 清理完成页面
 */
public class CompletedActivity extends BaseActivity implements View.OnClickListener {

    private ActivityCompletedBinding binding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCompletedBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 初始化工具栏
        setupToolbar(binding.toolbar);

        // 设置点击事件
        setupClickListeners();
    }


    private void setupClickListeners() {
        binding.junkFilesCategory.setOnClickListener(this);
        binding.imagesCategory.setOnClickListener(this);
        binding.videosCategory.setOnClickListener(this);
        binding.largeFilesCategory.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.junk_files_category) {
            startActivity(new Intent(this, CleanupScanActivity.class));
        } else if (id == R.id.images_category) {
            // 跳转到图片页面
            startActivity(new Intent(this, ImagesActivity.class));
        } else if (id == R.id.videos_category) {
            startActivity(new Intent(this, VideosActivity.class));
        } else if (id == R.id.large_files_category) {
            startActivity(new Intent(this, LargeFilesActivity.class));
        }
    }
}
