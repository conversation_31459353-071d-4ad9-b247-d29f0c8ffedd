package com.mobile.app.zencleaner.ui.activities;

import com.mobile.app.zencleaner.R;
import com.mobile.app.zencleaner.ui.viewmodels.FileViewModel;

/**
 * 视频文件管理页面
 */
public class VideosActivity extends ImagesActivity {

    @Override
    protected int getMediaType() {
        return FileViewModel.MEDIA_TYPE_VIDEO;
    }

    @Override
    protected long getFileSizeThreshold() {
        return 0; // 不过滤大小
    }

    @Override
    protected String getToolbarTitle() {
        return getString(R.string.video_files);
    }
}
