package com.mobile.app.zencleaner.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.mobile.app.zencleaner.R;
import com.mobile.app.zencleaner.models.JunkCategory;
import com.mobile.app.zencleaner.util.Tools;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CleanupItemListAdapter extends RecyclerView.Adapter<CleanupItemListAdapter.ItemViewHolder> {

    private List<JunkCategory> categoryList;
    private OnItemSelectionChangedListener selectionListener;

    // Map to store animations for each ViewHolder position
    private final Map<Integer, Animation> animationMap = new HashMap<>();

    public interface OnItemSelectionChangedListener {
        void onSelectionChanged(List<JunkCategory> categories);
    }

    public CleanupItemListAdapter(List<JunkCategory> categoryList) {
        this.categoryList = categoryList;
    }

    /**
     * Set a listener to be notified when selection changes
     */
    public void setOnSelectionChangedListener(OnItemSelectionChangedListener listener) {
        this.selectionListener = listener;
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_category, parent, false);
        return new ItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ItemViewHolder holder, int position) {
        JunkCategory category = categoryList.get(position);

        // Set basic category information
        bindCategoryInfo(holder, category);

        // Handle status icon based on scanning state
        handleStatusIconState(holder, category, position);
    }

    private void bindCategoryInfo(ItemViewHolder holder, JunkCategory category) {
        // Set category name and description
        holder.categoryNameView.setText(category.getName());
        holder.categoryDescView.setText(category.getDescription());

        // Set size text with appropriate formatting
        if (category.isScanning()) {
            holder.categorySizeView.setText("Scanning...");
        } else if (category.getSizeBytes() == 0) {
            holder.categorySizeView.setText("0 B");
        } else {
            String sizeText = Tools.formatSize(category.getSizeBytes());
            holder.categorySizeView.setText(sizeText);
        }

        // Set icon based on category type
        configureCategoryIcon(holder.categoryIconView, category.getName());
    }

    private void handleStatusIconState(ItemViewHolder holder, JunkCategory category, int position) {
        if (category.isScanning()) {
            // Set refresh icon and start rotation animation
            holder.statusIconView.setImageResource(R.mipmap.ic_refresh);

            Animation rotateAnimation = AnimationUtils.loadAnimation(holder.statusIconView.getContext(), R.anim.rotate_refresh);
            holder.statusIconView.startAnimation(rotateAnimation);

            // Store the animation for this position
            animationMap.put(position, rotateAnimation);

            // Disable click listener during scanning
            holder.statusIconView.setOnClickListener(null);
        } else {
            // Stop animation if it exists
            Animation animation = animationMap.get(position);
            if (animation != null) {
                holder.statusIconView.clearAnimation();
                animationMap.remove(position);
            }

            // Set checkbox icon based on selection state
            holder.statusIconView.setImageResource(
                    category.isSelected() ?
                    R.mipmap.ic_checkbox_checked_green :
                    R.mipmap.ic_checkbox_normal
            );

            // Enable click listener after scanning is complete
            holder.statusIconView.setOnClickListener(v -> {
                category.setSelected(!category.isSelected());
                holder.statusIconView.setImageResource(
                        category.isSelected() ?
                        R.mipmap.ic_checkbox_checked_green :
                        R.mipmap.ic_checkbox_normal
                );

                // Notify the listener about selection change
                if (selectionListener != null) {
                    selectionListener.onSelectionChanged(categoryList);
                }

                notifyDataSetChanged();
            });
        }
    }

    @Override
    public int getItemCount() {
        return categoryList.size();
    }

    public List<JunkCategory> getCategoryList() {
        return categoryList;
    }

    public void refreshCategoryList(List<JunkCategory> newCategoryList) {
        // Clear all animations when updating items
        for (Animation animation : animationMap.values()) {
            if (animation != null) {
                animation.cancel();
            }
        }
        animationMap.clear();

        this.categoryList = newCategoryList;
        notifyDataSetChanged();
    }

    public void addCategoryItem(JunkCategory category) {
        categoryList.add(category);
        notifyItemInserted(categoryList.size() - 1);
    }

    /**
     * Updates the scanning state of a category at the specified position
     */
    public void updateCategoryScanState(int position, boolean isScanning) {
        if (position >= 0 && position < categoryList.size()) {
            JunkCategory category = categoryList.get(position);
            if (category.isScanning() != isScanning) {
                // Create a new category with updated scanning state
                JunkCategory updatedCategory = new JunkCategory(
                    category.getId(),
                    category.getName(),
                    category.getDescription(),
                    category.getSizeBytes(),
                    category.getIconResId(),
                    isScanning,
                    !isScanning,
                    category.isSelected()
                );
                categoryList.set(position, updatedCategory);
                notifyItemChanged(position);
            }
        }
    }

    /**
     * Cleans up all animations - call this method in onDestroy of the activity
     */
    public void cleanupAllAnimations() {
        for (Animation animation : animationMap.values()) {
            if (animation != null) {
                animation.cancel();
            }
        }
        animationMap.clear();
    }

    private void configureCategoryIcon(ImageView iconView, String categoryName) {
        // Set appropriate icon based on category name
        if (categoryName.contains("Cache")) {
            iconView.setImageResource(android.R.drawable.ic_menu_recent_history);
        } else if (categoryName.contains("APK")) {
            iconView.setImageResource(android.R.drawable.ic_menu_set_as);
        } else if (categoryName.contains("Empty")) {
            iconView.setImageResource(android.R.drawable.ic_menu_crop);
        } else if (categoryName.contains("Log")) {
            iconView.setImageResource(android.R.drawable.ic_menu_agenda);
        } else {
            iconView.setImageResource(android.R.drawable.ic_menu_delete);
        }
    }

    static class ItemViewHolder extends RecyclerView.ViewHolder {
        final TextView categoryNameView;
        final TextView categoryDescView;
        final TextView categorySizeView;
        final ImageView categoryIconView;
        final ImageView statusIconView;

        ItemViewHolder(View view) {
            super(view);
            categoryNameView = view.findViewById(R.id.tv_category_name);
            categoryDescView = view.findViewById(R.id.tv_category_description);
            categorySizeView = view.findViewById(R.id.tv_size);
            categoryIconView = view.findViewById(R.id.iv_category_icon);
            statusIconView = view.findViewById(R.id.iv_status);
        }
    }
}
