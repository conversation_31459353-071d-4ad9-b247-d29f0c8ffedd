package com.mobile.app.zencleaner.models;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Java version of JunkCategory
 * Represents a category of junk files that can be cleaned
 */
public class JunkCategory {
    private final String id;
    private final String name;
    private final String description;
    private final long sizeBytes;
    private final @DrawableRes int iconResId;
    private final boolean isScanning;
    private final boolean isScanFinished;
    private boolean selected; // Added selected property

    /**
     * Constructor for JunkCategory
     */
    public JunkCategory(String id, String name, String description, long sizeBytes,
                        @DrawableRes int iconResId, boolean isScanning, boolean isScanFinished) {
        this(id, name, description, sizeBytes, iconResId, isScanning, isScanFinished, false);
    }

    /**
     * Constructor with selected parameter
     */
    public JunkCategory(String id, String name, String description, long sizeBytes,
                        @DrawableRes int iconResId, boolean isScanning, boolean isScanFinished, boolean selected) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.sizeBytes = sizeBytes;
        this.iconResId = iconResId;
        this.isScanning = isScanning;
        this.isScanFinished = isScanFinished;
        this.selected = selected;
    }

    /**
     * Constructor with default values
     */
    public JunkCategory(String id, String name, @DrawableRes int iconResId) {
        this(id, name, "", 0L, iconResId, false, false, false);
    }

    /**
     * Simple constructor for junk scanning
     */
    public JunkCategory(String name, String description, long sizeBytes, boolean selected) {
        this(name.toLowerCase().replace(" ", "_"), name, description, sizeBytes,
             android.R.drawable.ic_menu_delete, false, true, selected);
    }

    // Getters
    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public long getSizeBytes() {
        return sizeBytes;
    }

    public @DrawableRes int getIconResId() {
        return iconResId;
    }

    public boolean isScanning() {
        return isScanning;
    }

    public boolean isScanFinished() {
        return isScanFinished;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    // Copy method to replace Kotlin's copy() function
    public JunkCategory copy(String id, String name, String description, Long sizeBytes,
                            Integer iconResId, Boolean isScanning, Boolean isScanFinished, Boolean selected) {
        return new JunkCategory(
                id != null ? id : this.id,
                name != null ? name : this.name,
                description != null ? description : this.description,
                sizeBytes != null ? sizeBytes : this.sizeBytes,
                iconResId != null ? iconResId : this.iconResId,
                isScanning != null ? isScanning : this.isScanning,
                isScanFinished != null ? isScanFinished : this.isScanFinished,
                selected != null ? selected : this.selected
        );
    }

    // Overloaded copy method without selected parameter for backward compatibility
    public JunkCategory copy(String id, String name, String description, Long sizeBytes,
                            Integer iconResId, Boolean isScanning, Boolean isScanFinished) {
        return copy(id, name, description, sizeBytes, iconResId, isScanning, isScanFinished, null);
    }

    // Overloaded copy methods for convenience
    public JunkCategory copy(String description, long sizeBytes, boolean isScanning, boolean isScanFinished) {
        return copy(null, null, description, sizeBytes, null, isScanning, isScanFinished);
    }

    public JunkCategory copy(boolean isScanning) {
        return copy(null, null, null, null, null, isScanning, null);
    }

    public JunkCategory copy(long sizeBytes) {
        return copy(null, null, null, sizeBytes, null, null, null);
    }

    // Static factory method to replace Kotlin's companion object
    public static List<JunkCategory> createInitialCategories() {
        List<JunkCategory> categories = new ArrayList<>();
        categories.add(new JunkCategory(
                "cache",
                "App Cache",
                android.R.drawable.ic_menu_save // Placeholder icon
        ));
        categories.add(new JunkCategory(
                "apk",
                "Installation Files (APKs)",
                android.R.drawable.ic_menu_upload // Placeholder icon
        ));
        categories.add(new JunkCategory(
                "empty_folders",
                "Empty Folders",
                android.R.drawable.ic_menu_delete // Placeholder icon
        ));
        categories.add(new JunkCategory(
                "temp",
                "Temporary Files",
                android.R.drawable.ic_menu_recent_history // Placeholder icon
        ));
        return categories;
    }

    // Override equals and hashCode for proper comparison
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JunkCategory that = (JunkCategory) o;
        return sizeBytes == that.sizeBytes &&
                isScanning == that.isScanning &&
                isScanFinished == that.isScanFinished &&
                selected == that.selected &&
                Objects.equals(id, that.id) &&
                Objects.equals(name, that.name) &&
                Objects.equals(description, that.description) &&
                iconResId == that.iconResId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, description, sizeBytes, iconResId, isScanning, isScanFinished, selected);
    }

    @NonNull
    @Override
    public String toString() {
        return "JunkCategory{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", sizeBytes=" + sizeBytes +
                ", isScanning=" + isScanning +
                ", isScanFinished=" + isScanFinished +
                ", selected=" + selected +
                '}';
    }
}
