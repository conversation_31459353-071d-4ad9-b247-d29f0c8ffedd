package com.mobile.app.zencleaner.ui.activities;

import android.animation.ObjectAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsControllerCompat;

import com.mobile.app.zencleaner.databinding.ActivityLauncherBinding;

/**
 * Java version of SplashScreen
 * Displays a splash screen with a progress bar
 */
public class SplashScreenActivity extends AppCompatActivity {

    private ActivityLauncherBinding binding;
    private Handler uiHandler;
    private static final int SPLASH_DISPLAY_DURATION = 2000; // 2 seconds

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Use ViewBinding
        binding = ActivityLauncherBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Initialize UI components
        configureStatusBarTransparency();
        initializeLoadingViews();
        startLoadingSequence();
    }

    private void configureStatusBarTransparency() {
        // Set up transparent status bar
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        getWindow().setStatusBarColor(android.graphics.Color.TRANSPARENT);
        WindowInsetsControllerCompat windowInsetsController = new WindowInsetsControllerCompat(
                getWindow(), getWindow().getDecorView());
        windowInsetsController.setAppearanceLightStatusBars(true);
    }

    private void initializeLoadingViews() {
        // Initialize views
        TextView loadingText = binding.loadingText;
        loadingText.setText("Loading...");

        // Initialize handler for progress updates
        uiHandler = new Handler(Looper.getMainLooper());
    }

    private void startLoadingSequence() {
        // Start progress animation
        beginProgressAnimation(binding.progressBar);
    }

    private void beginProgressAnimation(ProgressBar progressBar) {
        // Create a smooth progress animation
        ObjectAnimator loadingProgressAnimator = ObjectAnimator.ofInt(progressBar, "progress", 0, 100);
        loadingProgressAnimator.setDuration(1500); // 1.5 seconds total
        loadingProgressAnimator.start();

        // After animation completes, navigate to home
        uiHandler.postDelayed(this::navigateToHomeScreen, SPLASH_DISPLAY_DURATION);
    }

    private void navigateToHomeScreen() {
        Intent homeIntent = new Intent(this, HomeActivity.class);
        startActivity(homeIntent);
        finish(); // Close this activity
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Remove any pending callbacks to prevent memory leaks
        if (uiHandler != null) {
            uiHandler.removeCallbacksAndMessages(null);
        }
        binding = null;
    }
}
