package com.mobile.app.zencleaner.ui.viewmodel;

import android.app.Application;
import android.os.Environment;
import android.os.StatFs;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import java.io.File;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * ViewModel for the main screen
 * Handles storage information and junk scanning
 */
public class MainViewModel extends AndroidViewModel {

    private final MutableLiveData<MainScreenState> uiState = new MutableLiveData<>(new MainScreenState());
    private final MutableLiveData<Long> navigateToJunkResult = new MutableLiveData<>();

    private final ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();

    public MainViewModel(@NonNull Application application) {
        super(application);

        // Initialize with current storage info
        updateStorageInfo();

        // Schedule periodic updates of storage info
        executor.scheduleAtFixedRate(this::updateStorageInfo, 30, 30, TimeUnit.SECONDS);
    }

    /**
     * Get the UI state
     *
     * @return the UI state as LiveData
     */
    public LiveData<MainScreenState> getUiState() {
        return uiState;
    }

    /**
     * Get the navigation event to the junk result screen
     *
     * @return the navigation event as LiveData
     */
    public LiveData<Long> getNavigateToJunkResult() {
        return navigateToJunkResult;
    }

    /**
     * Update the storage information
     */
    private void updateStorageInfo() {
        try {
            File externalStorageDirectory = Environment.getExternalStorageDirectory();
            StatFs statFs = new StatFs(externalStorageDirectory.getPath());

            long blockSize = statFs.getBlockSizeLong();
            long totalBlocks = statFs.getBlockCountLong();
            long availableBlocks = statFs.getAvailableBlocksLong();

            long totalSize = totalBlocks * blockSize;
            long availableSize = availableBlocks * blockSize;
            long usedSize = totalSize - availableSize;

            float usagePercentage = ((float) usedSize / totalSize) * 100f;

            // Convert to GB for display
            int usedGB = (int) (usedSize / (1024 * 1024 * 1024));
            int totalGB = (int) (totalSize / (1024 * 1024 * 1024));

            // 确保百分比在0-100之间
            usagePercentage = Math.max(0f, Math.min(100f, usagePercentage));

            // 添加日志输出
            android.util.Log.d("MainViewModel", "Storage stats: usagePercentage=" + usagePercentage + "%, usedGB=" + usedGB + "GB, totalGB=" + totalGB + "GB");

            MainScreenState currentState = uiState.getValue();
            if (currentState != null) {
                MainScreenState newState = new MainScreenState(usagePercentage, usedGB, totalGB, currentState.isScanning());
                uiState.postValue(newState);
            }
        } catch (Exception e) {
            android.util.Log.e("MainViewModel", "Error updating storage info: " + e.getMessage(), e);
            e.printStackTrace();
        }
    }

    /**
     * Handle the optimize button click
     * Starts a quick scan and navigates to the junk removal screen
     */

    @Override
    protected void onCleared() {
        super.onCleared();
        executor.shutdown();
    }

    /**
     * State class for the main screen
     */
    public static class MainScreenState {
        private final float usagePercentage;
        private final int storageUsedGb;
        private final int storageTotalGb;
        private final boolean scanning;

        public MainScreenState() {
            this(0f, 0, 0, false);
        }

        public MainScreenState(float usagePercentage, int storageUsedGb, int storageTotalGb, boolean scanning) {
            this.usagePercentage = usagePercentage;
            this.storageUsedGb = storageUsedGb;
            this.storageTotalGb = storageTotalGb;
            this.scanning = scanning;
        }

        public float getUsagePercentage() {
            return usagePercentage;
        }

        public int getStorageUsedGb() {
            return storageUsedGb;
        }

        public int getStorageTotalGb() {
            return storageTotalGb;
        }

        public boolean isScanning() {
            return scanning;
        }

    }
}
