package com.mobile.app.zencleaner.ui.activities;

import com.mobile.app.zencleaner.R;
import com.mobile.app.zencleaner.ui.viewmodels.FileViewModel;

/**
 * 大文件管理页面
 */
public class LargeFilesActivity extends FileActivity {

    @Override
    protected int getMediaType() {
        return FileViewModel.MEDIA_TYPE_LARGE_FILES;
    }

    @Override
    protected long getFileSizeThreshold() {
        return 5 * 1024 * 1024L; // 5MB
    }

    @Override
    protected String getToolbarTitle() {
        return getString(R.string.large_files);
    }
}
