package com.mobile.app.zencleaner.ui.views;

import android.graphics.Canvas;
import android.view.View;

public class CustomDrawView extends View {
    private OnDrawListener drawListener;

    public CustomDrawView(android.content.Context context) {
        super(context);
    }

    public CustomDrawView(android.content.Context context, android.util.AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomDrawView(android.content.Context context, android.util.AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setOnDrawListener(OnDrawListener listener) {
        this.drawListener = listener;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (drawListener != null) {
            drawListener.onDraw(canvas);
        }
    }


    // Custom view interface for drawing
    public interface OnDrawListener {
        void onDraw(Canvas canvas);
    }
}
