package com.mobile.app.zencleaner.ui.activities;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.app.Dialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;

import com.mobile.app.zencleaner.R;
import com.mobile.app.zencleaner.databinding.ActivityMainBinding;
import com.mobile.app.zencleaner.util.Screen;
import com.mobile.app.zencleaner.ui.viewmodels.MainViewModel;
import com.mobile.app.zencleaner.ui.widgets.CustomDrawView;

/**
 * Java version of MainActivity
 * This is the main entry point for the Java implementation of the app
 */
public class MainActivity extends BaseActivity {

    private ActivityMainBinding binding;
    private MainViewModel viewModel;

    // Progress animation variables
    private ValueAnimator progressAnimator;
    private float currentSweepAngle = 0f;
    private Paint progressPaint;
    private RectF progressRect;
    private boolean isAnimating = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Use ViewBinding
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Initialize ViewModel
        viewModel = new ViewModelProvider(this).get(MainViewModel.class);

        // 初始化权限请求launcher
        initPermissionLauncher();

        // Initialize views
        setupProgressDrawing();
        setupClickListeners();
        observeViewModel();
    }

    private void initPermissionLauncher() {
        permissionLauncher = registerForActivityResult(new ActivityResultContracts.RequestMultiplePermissions(), result -> {
            boolean allGranted = true;
            for (Boolean granted : result.values()) {
                if (!granted) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                // 所有权限都已授予，导航到目标页面
                if (pendingActivityClass != null) {
                    navigateTo(pendingActivityClass);
                    pendingActivityClass = null;
                }
            } else {
                // 权限被拒绝
                Toast.makeText(this, "Permission denied. Some features may not work properly.", Toast.LENGTH_SHORT).show();
            }
        });
    }

    // Permission request launcher
    private ActivityResultLauncher<String[]> permissionLauncher;
    private String[] requiredPermissions;
    private Class<?> pendingActivityClass;

    private void setupClickListeners() {
        // Settings button
        ImageView settingsButton = binding.btnSettings;
        settingsButton.setOnClickListener(v -> {
            navigateTo(MoreActivity.class);
        });

        // Scan now button
        binding.btnScanNow.setOnClickListener(v -> {
            checkAndRequestPermissions(ScanActivity.class, Screen.JUNK_SCAN);
        });

        // Feature cards
        binding.cardJunk.setOnClickListener(v -> checkAndRequestPermissions(ScanActivity.class, Screen.JUNK_SCAN));

        binding.cardImages.setOnClickListener(v -> checkAndRequestPermissions(ImagesActivity.class, Screen.IMAGES));

        binding.cardVideos.setOnClickListener(view -> checkAndRequestPermissions(VideosActivity.class, Screen.VIDEOS));

        binding.cardLargeFiles.setOnClickListener(v -> checkAndRequestPermissions(LargeFilesActivity.class, Screen.LARGE_FILES));
    }

    private <T> void navigateTo(Class<T> activityClass) {
        Intent intent = new Intent(this, activityClass);
        startActivity(intent);
    }

    private <T> void checkAndRequestPermissions(Class<T> activityClass, Screen screen) {
        pendingActivityClass = activityClass;

        // 根据不同的屏幕类型确定所需权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) { // Android 13+
            if (screen == Screen.IMAGES) {
                requiredPermissions = new String[]{Manifest.permission.READ_MEDIA_IMAGES};
            } else if (screen == Screen.AUDIOS) {
                requiredPermissions = new String[]{Manifest.permission.READ_MEDIA_AUDIO};
            } else if (screen == Screen.VIDEOS) {
                requiredPermissions = new String[]{Manifest.permission.READ_MEDIA_VIDEO};
            } else if (screen == Screen.LARGE_FILES || screen == Screen.JUNK_SCAN) {
                requiredPermissions = new String[]{Manifest.permission.READ_MEDIA_IMAGES, Manifest.permission.READ_MEDIA_VIDEO, Manifest.permission.READ_MEDIA_AUDIO};
            } else {
                // 其他页面不需要特殊权限
                navigateTo(activityClass);
                return;
            }
        } else {
            // Android 13以下使用READ_EXTERNAL_STORAGE
            requiredPermissions = new String[]{Manifest.permission.READ_EXTERNAL_STORAGE};
        }

        // 检查是否已经有权限
        boolean hasAllPermissions = true;
        for (String permission : requiredPermissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                hasAllPermissions = false;
                break;
            }
        }

        if (hasAllPermissions) {
            // 已有所有权限，直接导航
            navigateTo(activityClass);
        } else {
            // 显示权限对话框
            showPermissionDialog(screen);
        }
    }

    private void showPermissionDialog(Screen screen) {
        Dialog dialog = new Dialog(this, R.style.TransparentDialog);
        dialog.setContentView(R.layout.dialog_permission);
        dialog.setCancelable(true);
        dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);

        // 设置权限提示文本
        TextView messageText = dialog.findViewById(R.id.tv_permission_message);
        messageText.setText("Permission is denied.\nPlease authorize the phone settings.");

        // 设置按钮点击事件
        Button cancelButton = dialog.findViewById(R.id.btn_cancel);
        Button confirmButton = dialog.findViewById(R.id.btn_confirm);

        cancelButton.setOnClickListener(v -> dialog.dismiss());

        confirmButton.setOnClickListener(v -> {
            dialog.dismiss();
            // 请求权限
            permissionLauncher.launch(requiredPermissions);
        });

        dialog.show();
    }

    private void observeViewModel() {
        // Observe UI state
        viewModel.getUiState().observe(this, state -> {
            // Update storage info
            updateStorageInfo(state.getUsagePercentage(), state.getStorageUsedGb(), state.getStorageTotalGb());

            // Handle scanning state
            binding.btnScanNow.setEnabled(!state.isScanning());
        });

        // Observe navigation events
//        viewModel.getNavigateToJunkResult().observe(this, scanResult -> {
//            if (scanResult != null && scanResult > 0) {
//                // Navigate to junk removal with scan result
//                Intent intent = new Intent(this, JunkRemovalActivity.class);
//                intent.putExtra("scanResult", scanResult);
//                startActivity(intent);
//            }
//        });
    }

    private void updateStorageInfo(float usagePercentage, int usedGb, int totalGb) {
        // Calculate target sweep angle (0-360 degrees)
        float targetSweepAngle = (usagePercentage / 100f) * 360f;

        // 强制重置当前进度并开始新的动画
        // 确保每次进入页面都会显示动画
        currentSweepAngle = 0f;
        animateProgress(targetSweepAngle);

        // 更新存储文本，使用与UI效果图一致的格式
        binding.tvStorageUsed.setText(usedGb + "GB / " + totalGb + "GB");

        // 初始化百分比文本，开始时显示0
        binding.tvStoragePercentage.setText("0");
    }

    private void animateProgress(float targetSweepAngle) {
        // Cancel any running animation
        if (progressAnimator != null && progressAnimator.isRunning()) {
            progressAnimator.cancel();
        }

        isAnimating = true;

        // Create and start the progress animation
        progressAnimator = ValueAnimator.ofFloat(0f, targetSweepAngle);
        progressAnimator.setDuration(1500); // 1.5 seconds - 更快的动画速度
        progressAnimator.setInterpolator(new LinearInterpolator());

        progressAnimator.addUpdateListener(animation -> {
            currentSweepAngle = (float) animation.getAnimatedValue();
            binding.progressArc.invalidate(); // Redraw the progress arc

            // Update percentage text with animation
            int displayPercentage = Math.round((currentSweepAngle / 360f) * 100f);
            binding.tvStoragePercentage.setText(String.valueOf(displayPercentage));

            // 更新圆点指示器的位置
            updateCapPosition(currentSweepAngle);
        });

        progressAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                // 确保圆点指示器在动画开始时可见
//                binding.progressCap.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                isAnimating = false;

                // 确保动画结束时显示正确的百分比
                int finalPercentage = Math.round((targetSweepAngle / 360f) * 100f);
                binding.tvStoragePercentage.setText(String.valueOf(finalPercentage));
            }
        });

        progressAnimator.start();
    }

    private void setupProgressDrawing() {
        // Initialize paint for progress arc
        progressPaint = new Paint();
        progressPaint.setColor(ContextCompat.getColor(this, R.color.colorPrimary)); // 与UI效果图一致的紫色
        progressPaint.setStyle(Paint.Style.STROKE);
        progressPaint.setStrokeWidth(60);
        progressPaint.setAntiAlias(true);
        progressPaint.setStrokeCap(Paint.Cap.ROUND);

        // Initialize rect for progress arc
        progressRect = new RectF();

        // Set up custom drawing for progress arc
        binding.progressArc.setLayerType(View.LAYER_TYPE_HARDWARE, null); // Hardware acceleration

        // Cast to our custom view
        if (binding.progressArc instanceof CustomDrawView) {
            CustomDrawView customView = (CustomDrawView) binding.progressArc;
            customView.setOnDrawListener(canvas -> {
                int width = binding.progressArc.getWidth();
                int height = binding.progressArc.getHeight();

                // 计算圆环的矩形区域，留出足够的边距
                float padding = progressPaint.getStrokeWidth() / 2 + 40; // 增加额外的边距
                progressRect.set(padding, padding, width - padding, height - padding);

                // 绘制背景轨道（浅灰色圆环）
                Paint trackPaint = new Paint(progressPaint);
                trackPaint.setColor(Color.parseColor("#eef2ff"));
                trackPaint.setAlpha(200); // 调整透明度，使其看起来更淡
                canvas.drawArc(progressRect, 0, 360, false, trackPaint);

                // 绘制进度弧（紫色部分）
                // 使用渐变色来增强视觉效果
                Paint gradientPaint = new Paint(progressPaint);
                gradientPaint.setShader(new android.graphics.LinearGradient(0, 0, width, height, new int[]{Color.parseColor("#556AF7"), Color.parseColor("#A3D7FF")}, null, android.graphics.Shader.TileMode.CLAMP));

                canvas.drawArc(progressRect, -90, currentSweepAngle, false, gradientPaint);

                // Update the cap position if the progress is not 0
                if (currentSweepAngle > 0) {
                    updateCapPosition(currentSweepAngle);
                }
            });
        }
    }

    private void updateCapPosition(float sweepAngle) {
        // Visibility Check (remains the same)
        if (sweepAngle <= 0.1f) {
            if (binding.progressCap.getVisibility() != View.INVISIBLE) {
                binding.progressCap.setVisibility(View.INVISIBLE);
            }
            return;
        } else {
            if (binding.progressCap.getVisibility() != View.VISIBLE) {
//                binding.progressCap.setVisibility(View.VISIBLE);
            }
        }

        // Get View Dimensions (remains the same)
        int viewWidth = binding.progressArc.getWidth();
        int viewHeight = binding.progressArc.getHeight();
        if (viewWidth <= 0 || viewHeight <= 0 || progressPaint == null) {
            Log.w("MainActivity", "updateCapPosition: View dimensions or paint not ready.");
            return;
        }

        // --- START OF CRITICAL FIX ---
        // Recalculate Arc Geometry EXACTLY as done in onDraw

        // 1. Get stroke width
        float strokeWidth = progressPaint.getStrokeWidth();

        // 2. Calculate padding USING THE SAME +40 as in onDraw
        float padding = strokeWidth / 2f + 40f; // <--- MATCHES onDraw!

        // 3. Determine the bounds of the arc's centerline rectangle
        float arcRectLeft = padding;
        float arcRectTop = padding;
        float arcRectRight = viewWidth - padding;
        float arcRectBottom = viewHeight - padding;

        // 4. Calculate the center point of this specific arc rectangle
        float centerX = (arcRectLeft + arcRectRight) / 2f;
        float centerY = (arcRectTop + arcRectBottom) / 2f;

        // 5. Calculate the radius based on this specific arc rectangle
        float radius = (arcRectRight - arcRectLeft) / 2f; // Radius for the centerline

        // --- END OF CRITICAL FIX ---

        // Exit if calculated radius is invalid
        if (radius <= 0) {
            Log.w("MainActivity", "updateCapPosition: Calculated radius (" + radius + ") is invalid using padding " + padding);
            return;
        }

        // Calculate Angle (remains the same)
        float angleDegrees = -90f + sweepAngle;
        float angleRadians = (float) Math.toRadians(angleDegrees);

        // Calculate Target Cap Center (remains the same)
        float capTargetCenterX = centerX + radius * (float) Math.cos(angleRadians);
        float capTargetCenterY = centerY + radius * (float) Math.sin(angleRadians);

        // Get Cap View Dimensions (remains the same)
        int capWidth = binding.progressCap.getWidth();
        int capHeight = binding.progressCap.getHeight();
        float density = getResources().getDisplayMetrics().density;
        if (capWidth <= 0) capWidth = (int) (20 * density); // Use your XML dp value
        if (capHeight <= 0) capHeight = (int) (20 * density); // Use your XML dp value

        // Calculate Cap View's Top-Left for Layout (remains the same)
        float capX = capTargetCenterX - capWidth / 2f;
        float capY = capTargetCenterY - capHeight / 2f;


        // Apply Position (remains the same)
        binding.progressCap.setX(capX);
        binding.progressCap.setY(capY);
        binding.progressCap.bringToFront();
    }


    @Override
    protected void onResume() {
        super.onResume();
        // 每次进入页面时刷新存储信息
        // 重置当前进度并强制更新UI
        currentSweepAngle = 0f;

        // 强制重新加载存储信息
        // 获取当前状态
        MainViewModel.MainScreenState state = viewModel.getUiState().getValue();
        if (state != null) {
            // 更新存储信息
            updateStorageInfo(state.getUsagePercentage(), state.getStorageUsedGb(), state.getStorageTotalGb());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (progressAnimator != null) {
            progressAnimator.cancel();
        }
        binding = null;
    }
}
