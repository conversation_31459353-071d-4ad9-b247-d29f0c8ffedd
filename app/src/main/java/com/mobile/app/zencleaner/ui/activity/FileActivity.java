package com.mobile.app.zencleaner.ui.activity;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.IntentSenderRequest;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.RecyclerView;

import com.mobile.app.zencleaner.adapters.FileAdapter;
import com.mobile.app.zencleaner.models.SelectableFile;
import com.mobile.app.zencleaner.databinding.ActivityFileBinding;
import com.mobile.app.zencleaner.utils.Utils;
import com.mobile.app.zencleaner.ui.viewmodel.MediaViewModel;

import java.util.ArrayList;
import java.util.List;

public abstract class FileActivity extends AppCompatActivity implements FileAdapter.OnFileItemClickListener {
    private static final String TAG = "MediaActivity";
    private static final int PERMISSION_REQUEST_CODE = 1001;

    protected ActivityFileBinding binding;
    protected MediaViewModel viewModel;
    protected FileAdapter adapter;

    // UI Components
    protected RecyclerView recyclerView;
    protected ProgressBar progressBar;
    protected LinearLayout permissionView;
    protected LinearLayout errorView;
    protected LinearLayout emptyView;
    protected View bottomBar;
    protected CheckBox selectAllCheckBox;
    protected View selectAllButton;
    protected TextView selectedSizeText;
    protected Button removeButton;
    protected TextView errorMessageText;
    protected TextView permissionMessageText;
    protected Button retryButton;
    protected Button grantPermissionButton;

    // Permission handling
    protected final ActivityResultLauncher<String[]> requestPermissionLauncher = registerForActivityResult(new ActivityResultContracts.RequestMultiplePermissions(), result -> {
        boolean allGranted = true;
        for (Boolean granted : result.values()) {
            if (!granted) {
                allGranted = false;
                break;
            }
        }
        viewModel.onPermissionResult(allGranted);
    });

    // Deletion handling
    protected final ActivityResultLauncher<IntentSenderRequest> deletionLauncher = registerForActivityResult(new ActivityResultContracts.StartIntentSenderForResult(), result -> {
        if (result.getResultCode() == Activity.RESULT_OK) {
            // 删除成功
            viewModel.handleActivityResult(Activity.RESULT_OK);
            Toast.makeText(this, "Files deleted successfully", Toast.LENGTH_SHORT).show();
            startActivity(new Intent(this, CompletedActivity.class));
        } else {
            // 删除取消或失败
            viewModel.handleActivityResult(Activity.RESULT_CANCELED);
        }
    });

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            WindowInsetsControllerCompat controller = new WindowInsetsControllerCompat(getWindow(), getWindow().getDecorView());
            // Set status bar icons to dark
            controller.setAppearanceLightStatusBars(true);
        }

        // 使用当前活动的布局文件
        binding = ActivityFileBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Initialize ViewModel
        viewModel = new ViewModelProvider(this).get(MediaViewModel.class);

        // Set media type and threshold
        viewModel.setMediaTypeAndThreshold(getMediaType(), getFileSizeThreshold());

        // Initialize UI components
        initViews();
        setupToolbar();
        setupRecyclerView();
        setupBottomBar();
        setupClickListeners();

        // Observe ViewModel
        observeViewModel();

        // Check permissions
        checkPermissions();
    }

    // Abstract methods to be implemented by subclasses
    protected abstract int getMediaType();

    protected abstract long getFileSizeThreshold();

    protected abstract String getToolbarTitle();

    private void initViews() {
        recyclerView = binding.recyclerView;
        progressBar = binding.progressBar;
        permissionView = binding.permissionView;
        errorView = binding.errorView;
        emptyView = binding.emptyView;
        bottomBar = binding.bottomBar.getRoot();
        errorMessageText = binding.tvErrorMessage;
        permissionMessageText = binding.tvPermissionMessage;
        retryButton = binding.btnRetry;
        grantPermissionButton = binding.btnGrantPermission;

        // Bottom bar views
        selectAllCheckBox = binding.bottomBar.checkboxSelectAll;
        selectAllButton = binding.bottomBar.btnSelectAll;
        selectedSizeText = binding.bottomBar.tvSelectedSize;
        removeButton = binding.bottomBar.btnRemove;
    }

    private void setupToolbar() {
        Toolbar toolbar = binding.toolbar;
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        getSupportActionBar().setTitle(getToolbarTitle());
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }

    protected void setupRecyclerView() {
        adapter = new FileAdapter(this);
        recyclerView.setAdapter(adapter);
        recyclerView.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
    }

    protected void setupBottomBar() {
        selectAllCheckBox.setOnClickListener(v -> {
            viewModel.toggleSelectAll();
            if (adapter != null) {
                adapter.notifyDataSetChanged(); // 通知适配器数据已更改
            }
            updateUI(); // 更新UI状态
        });

        selectAllButton.setOnClickListener(v -> {
            viewModel.toggleSelectAll();
            if (adapter != null) {
                adapter.notifyDataSetChanged(); // 通知适配器数据已更改
            }
            updateUI(); // 更新UI状态
        });
    }

    private void setupClickListeners() {
        retryButton.setOnClickListener(v -> viewModel.loadFiles());

        grantPermissionButton.setOnClickListener(v -> {
            if (ActivityCompat.shouldShowRequestPermissionRationale(this, getReadStoragePermission())) {
                // 用户之前拒绝过权限，但没有选择"不再询问"
                requestStoragePermissions();
            } else {
                // 用户之前选择了"不再询问"，需要引导用户到设置页面
                openAppSettings();
            }
        });

        removeButton.setOnClickListener(v -> {
            if (viewModel.getSelectedFiles().isEmpty()) {
                Toast.makeText(this, "No files selected", Toast.LENGTH_SHORT).show();
                return;
            }
            viewModel.deleteSelectedFiles(deletionLauncher);
        });
    }

    protected void observeViewModel() {
        viewModel.getFiles().observe(this, files -> {
            adapter.updateFiles(files);
            updateUI();
        });

        viewModel.getIsLoading().observe(this, isLoading -> {
            progressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
            updateUI();
        });

        viewModel.getError().observe(this, error -> {
            if (error != null) {
                errorMessageText.setText(error);
            }
            updateUI();
        });

        viewModel.getPermissionGranted().observe(this, granted -> {
            updateUI();
        });

        viewModel.getShowPermissionDialog().observe(this, show -> {
            if (show) {
                showPermissionDialog();
            }
        });
    }

    protected void updateUI() {
        Boolean isLoading = viewModel.getIsLoading().getValue();
        Boolean permissionGranted = viewModel.getPermissionGranted().getValue();
        String error = viewModel.getError().getValue();
        List<SelectableFile> files = viewModel.getFiles().getValue();

        if (isLoading == null) isLoading = false;
        if (permissionGranted == null) permissionGranted = false;
        if (files == null) files = new ArrayList<>();

        // 更新视图可见性
        permissionView.setVisibility(!permissionGranted ? View.VISIBLE : View.GONE);
        errorView.setVisibility(error != null && !isLoading && permissionGranted ? View.VISIBLE : View.GONE);
        emptyView.setVisibility(files.isEmpty() && !isLoading && permissionGranted && error == null ? View.VISIBLE : View.GONE);
        recyclerView.setVisibility(!files.isEmpty() && !isLoading && permissionGranted ? View.VISIBLE : View.GONE);

        // 更新底部栏
        boolean hasFiles = !files.isEmpty();
        boolean hasSelectedFiles = !viewModel.getSelectedFiles().isEmpty();
        bottomBar.setVisibility(hasFiles && permissionGranted ? View.VISIBLE : View.GONE);
        removeButton.setEnabled(hasSelectedFiles);

        // 更新选中文件大小
        long totalSelectedSize = viewModel.getTotalSelectedSize();
        // 在删除按钮上显示选中文件大小
        removeButton.setText("Remove " + Utils.formatSize(totalSelectedSize));
        // 保留原来的文本显示，但隐藏起来
        selectedSizeText.setText("(" + Utils.formatSize(totalSelectedSize) + ")");
        selectedSizeText.setVisibility(View.GONE);

        // 更新全选复选框
        boolean allSelected = !files.isEmpty() && viewModel.getSelectedFiles().size() == files.size();
        selectAllCheckBox.setChecked(allSelected);
    }

    private void checkPermissions() {
        if (hasStoragePermission()) {
            viewModel.onPermissionResult(true);
        } else {
            requestStoragePermissions();
        }
    }

    private boolean hasStoragePermission() {
        return ContextCompat.checkSelfPermission(this, getReadStoragePermission()) == PackageManager.PERMISSION_GRANTED;
    }

    private String getReadStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return Manifest.permission.READ_MEDIA_IMAGES;
        } else {
            return Manifest.permission.READ_EXTERNAL_STORAGE;
        }
    }

    private void requestStoragePermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 需要请求多个媒体权限
            requestPermissionLauncher.launch(new String[]{Manifest.permission.READ_MEDIA_IMAGES, Manifest.permission.READ_MEDIA_VIDEO, Manifest.permission.READ_MEDIA_AUDIO});
        } else {
            // Android 12 及以下只需要请求存储权限
            requestPermissionLauncher.launch(new String[]{Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }

    private void showPermissionDialog() {
        new AlertDialog.Builder(this).setTitle("Permission Required").setMessage("Storage permission is needed to display files. Please grant the permission in app settings.").setPositiveButton("Open Settings", (dialog, which) -> openAppSettings()).setNegativeButton("Cancel", (dialog, which) -> viewModel.dismissPermissionDialog()).setOnCancelListener(dialog -> viewModel.dismissPermissionDialog()).show();
    }

    private void openAppSettings() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getPackageName(), null);
        intent.setData(uri);
        startActivity(intent);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            viewModel.onPermissionResult(allGranted);
        }
    }

    @Override
    public void onFileItemClick(SelectableFile file) {
        viewModel.toggleItemSelection(file);
        adapter.notifyDataSetChanged(); // 通知适配器数据已更改
        updateUI();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}
