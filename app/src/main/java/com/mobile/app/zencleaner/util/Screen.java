package com.mobile.app.zencleaner.util;

public enum Screen {
    SPLASH("splash"),
    PRIVACY_AGREEMENT("privacy_agreement"),
    HOME("home"),
    JUNK_SCAN("junk_scan"),
    JUNK_REMOVAL("junk_removal/{scanResult}"),
    IMAGES("images"),
    VIDEOS("videos"),
    AUDIOS("audios"),
    RESULT("result"),
    COMPLETED("Completed"),
    MORE("More"),
    LARGE_FILES("large_files_screen");

    private final String route;

    Screen(String route) {
        this.route = route;
    }

    public String getRoute() {
        return route;
    }

    /**
     * Create a route with parameters for JunkRemoval
     * @param scanResultBytes the scan result in bytes
     * @return the route with parameters
     */
    public String createRoute(long scanResultBytes) {
        if (this == JUNK_REMOVAL) {
            return "junk_removal/" + scanResultBytes;
        }
        return route;
    }

    /**
     * Get the argument name for JunkRemoval
     * @return the argument name
     */
    public static String getJunkRemovalArgName() {
        return "scanResult";
    }
}
