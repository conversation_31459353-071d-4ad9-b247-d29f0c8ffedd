package com.mobile.app.zencleaner.adapters;

import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.mobile.app.zencleaner.R;
import com.mobile.app.zencleaner.models.SelectableFile;
import com.mobile.app.zencleaner.util.Tools;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;

import java.util.ArrayList;
import java.util.List;

public class FileGridAdapter extends RecyclerView.Adapter<FileGridAdapter.ImageViewHolder> {

    private List<SelectableFile> files = new ArrayList<>();
    private final OnImageItemClickListener listener;

    public interface OnImageItemClickListener {
        void onImageItemClick(SelectableFile file);
    }

    public FileGridAdapter(OnImageItemClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ImageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_file_grid, parent, false);
        return new ImageViewHolder(view, listener);
    }

    @Override
    public void onBindViewHolder(@NonNull ImageViewHolder holder, int position) {
        holder.bind(files.get(position));
    }

    @Override
    public int getItemCount() {
        return files.size();
    }

    public void updateFiles(List<SelectableFile> newFiles) {
        this.files = newFiles;
        notifyDataSetChanged();
    }

    static class ImageViewHolder extends RecyclerView.ViewHolder {
        private final ImageView fileIcon;
        private final ImageView fileThumbnail;
        private final TextView fileName;
        private final TextView fileSize;
        private final CheckBox checkBox;
        private final OnImageItemClickListener listener;

        public ImageViewHolder(@NonNull View itemView, OnImageItemClickListener listener) {
            super(itemView);
            this.listener = listener;
            fileIcon = itemView.findViewById(R.id.file_icon);
            fileThumbnail = itemView.findViewById(R.id.file_thumbnail);
            fileName = itemView.findViewById(R.id.file_name);
            fileSize = itemView.findViewById(R.id.file_size);
            checkBox = itemView.findViewById(R.id.checkbox);

            // 设置整个项的点击监听器
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && listener != null) {
                    listener.onImageItemClick(getFileAtPosition(position));
                }
            });
        }

        private SelectableFile getFileAtPosition(int position) {
            return ((FileGridAdapter) getBindingAdapter()).files.get(position);
        }

        public void bind(SelectableFile file) {
            // 设置文件大小
            fileSize.setText(Tools.formatSize(file.getSizeBytes()));
            
            // 设置选中状态
            boolean isSelected = file.isSelected();
            checkBox.setChecked(isSelected);

            // 加载缩略图
            loadThumbnail(Uri.parse(file.getUri()));
        }

        private void loadThumbnail(Uri uri) {
            Context context = itemView.getContext();

            RequestOptions options = new RequestOptions()
                    .centerCrop()
                    .diskCacheStrategy(DiskCacheStrategy.ALL);

            Glide.with(context)
                    .load(uri)
                    .apply(options)
                    .into(fileThumbnail);

            // 显示缩略图，隐藏图标
            fileThumbnail.setVisibility(View.VISIBLE);
            fileIcon.setVisibility(View.GONE);
        }
    }
}
