package com.mobile.app.zencleaner.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.mobile.app.zencleaner.R;
import com.mobile.app.zencleaner.models.JunkCategory;
import com.mobile.app.zencleaner.util.Tools;

import java.util.List;

public class CategoryAdapter extends RecyclerView.Adapter<CategoryAdapter.ViewHolder> {

    private List<JunkCategory> items;

    public CategoryAdapter(List<JunkCategory> items) {
        this.items = items;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_category, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        JunkCategory category = items.get(position);

        // Set category name and description
        holder.nameText.setText(category.getName());
        holder.descriptionText.setText(category.getDescription());

        // Set size text with appropriate formatting
        String sizeText = Tools.formatSize(category.getSizeBytes());
        holder.sizeText.setText(sizeText);

        // 根据扫描状态设置大小文本
        if (category.isScanning()) {
            holder.sizeText.setText("Scanning...");
        } else if (category.getSizeBytes() == 0) {
            holder.sizeText.setText("0 B");
        }

        // Set icon based on category type
        setIconForCategory(holder.iconView, category.getName());

        // Set status icon based on selection
        holder.statusIcon.setImageResource(
                category.isSelected() ?
                R.mipmap.ic_checkbox_checked_green :
                R.mipmap.ic_checkbox_normal
        );

        // Set click listener to toggle selection
        holder.itemView.setOnClickListener(v -> {
            category.setSelected(!category.isSelected());
            holder.statusIcon.setImageResource(
                    category.isSelected() ?
                    R.mipmap.ic_checkbox_checked_green :
                    R.mipmap.ic_checkbox_normal
            );
        });
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    public List<JunkCategory> getItems() {
        return items;
    }

    public void updateItems(List<JunkCategory> newItems) {
        this.items = newItems;
        notifyDataSetChanged();
    }

    private void setIconForCategory(ImageView iconView, String categoryName) {
        // Set appropriate icon based on category name
        if (categoryName.contains("Cache")) {
            iconView.setImageResource(android.R.drawable.ic_menu_recent_history);
        } else if (categoryName.contains("APK")) {
            iconView.setImageResource(android.R.drawable.ic_menu_set_as);
        } else if (categoryName.contains("Empty")) {
            iconView.setImageResource(android.R.drawable.ic_menu_crop);
        } else if (categoryName.contains("Log")) {
            iconView.setImageResource(android.R.drawable.ic_menu_agenda);
        } else {
            iconView.setImageResource(android.R.drawable.ic_menu_delete);
        }
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        final TextView nameText;
        final TextView descriptionText;
        final TextView sizeText;
        final ImageView iconView;
        final ImageView statusIcon;

        ViewHolder(View view) {
            super(view);
            nameText = view.findViewById(R.id.tv_category_name);
            descriptionText = view.findViewById(R.id.tv_category_description);
            sizeText = view.findViewById(R.id.tv_size);
            iconView = view.findViewById(R.id.iv_category_icon);
            statusIcon = view.findViewById(R.id.iv_status);
        }
    }
}
