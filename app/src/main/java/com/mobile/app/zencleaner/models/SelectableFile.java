package com.mobile.app.zencleaner.models;

import java.util.Objects;

/**
 * Java version of SelectableFile
 * Represents a selectable file
 */
public class SelectableFile {
    private final long id;
    private final String name;
    private final String path;
    private final String uri;
    private final long sizeBytes;
    private final String mimeType;
    private final int mediaType;
    private boolean selected;

    /**
     * Constructor for SelectableFile
     */
    public SelectableFile(long id, String name, String path, String uri, long sizeBytes, String mimeType, int mediaType, boolean selected) {
        this.id = id;
        this.name = name;
        this.path = path;
        this.uri = uri;
        this.sizeBytes = sizeBytes;
        this.mimeType = mimeType;
        this.mediaType = mediaType;
        this.selected = selected;
    }

    /**
     * Constructor with default values for mimeType (null), mediaType (0), and selected (false)
     */
    public SelectableFile(long id, String name, String path, String uri, long sizeBytes) {
        this(id, name, path, uri, sizeBytes, null, 0, false);
    }

    /**
     * Constructor with default selection state (false)
     */
    public SelectableFile(long id, String name, String path, String uri, long sizeBytes, String mimeType, int mediaType) {
        this(id, name, path, uri, sizeBytes, mimeType, mediaType, false);
    }

    public long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getPath() {
        return path;
    }

    public String getUri() {
        return uri;
    }

    public long getSizeBytes() {
        return sizeBytes;
    }

    public String getMimeType() {
        return mimeType;
    }

    public int getMediaType() {
        return mediaType;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SelectableFile that = (SelectableFile) o;
        return id == that.id && sizeBytes == that.sizeBytes && mediaType == that.mediaType && Objects.equals(name, that.name) && Objects.equals(path, that.path) && Objects.equals(uri, that.uri) && Objects.equals(mimeType, that.mimeType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, path, uri, sizeBytes, mimeType, mediaType);
    }

    @Override
    public String toString() {
        return "SelectableFile{" + "id=" + id + ", name='" + name + '\'' + ", path='" + path + '\'' + ", uri='" + uri + '\'' + ", sizeBytes=" + sizeBytes + ", mimeType='" + mimeType + '\'' + ", mediaType=" + mediaType + ", selected=" + selected + '}';
    }
}
