package com.mobile.app.zencleaner.ui.activities;

import android.animation.ValueAnimator;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.mobile.app.zencleaner.R;
import com.mobile.app.zencleaner.adapters.CleanupItemListAdapter;
import com.mobile.app.zencleaner.databinding.ActivityScanBinding;
import com.mobile.app.zencleaner.models.JunkCategory;
import com.mobile.app.zencleaner.ui.viewmodels.ScanViewModel;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;

public class CleanupScanActivity extends AppCompatActivity {

    private ActivityScanBinding binding;
    private ScanViewModel scanningViewModel;
    private CleanupItemListAdapter categoryListAdapter;


    // Scanning simulation variables
    private final Handler uiHandler = new Handler(Looper.getMainLooper());
    private final Random randomGenerator = new Random();
    private int scanProgressValue = 0;
    private final List<String> scanPathList = new ArrayList<>();
    private int currentScanPathIndex = 0;
    private ValueAnimator scanProgressAnimator;
    private long totalDetectedJunkSize = 0;

    // Track generated category types to prevent duplicates
    private final Set<String> usedCategoryTypes = new HashSet<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            WindowInsetsControllerCompat controller = new WindowInsetsControllerCompat(getWindow(), getWindow().getDecorView());
            // Set status bar icons to dark
            controller.setAppearanceLightStatusBars(true);
        }

        binding = ActivityScanBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Initialize ViewModel
        scanningViewModel = new ViewModelProvider(this).get(ScanViewModel.class);

        // Initialize UI components
        initializeViews();
        configureToolbar();
        setupCategoryRecyclerView();
        configureClickListeners();

        // Start scanning animation
        prepareScanPaths();
        startScanAnimation();
    }

    private void initializeViews() {
        // Initially hide the scan status header
        binding.scanStatusHeader.setVisibility(View.GONE);
        binding.scanningView.setVisibility(View.VISIBLE);
    }

    private void configureToolbar() {
        Toolbar toolbar = binding.toolbar;
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        // Use system back arrow, no need to set additional icon
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }

    private void setupCategoryRecyclerView() {
        categoryListAdapter = new CleanupItemListAdapter(new ArrayList<>());

        // Set selection change listener
        categoryListAdapter.setOnSelectionChangedListener(categories -> {
            // Calculate total size of selected items
            long selectedSize = categories.stream()
                    .filter(JunkCategory::isSelected)
                    .mapToLong(JunkCategory::getSizeBytes)
                    .sum();

            // Update displayed total size
            displayJunkSize(selectedSize);
        });

        binding.recyclerJunkCategories.setLayoutManager(new LinearLayoutManager(this));
        binding.recyclerJunkCategories.setAdapter(categoryListAdapter);
    }

    private void configureClickListeners() {
        binding.btnNextStep.setOnClickListener(v -> {
            // Ensure at least one category is selected
            boolean hasSelectedCategory = false;
            for (JunkCategory category : categoryListAdapter.getCategoryList()) {
                if (category.isSelected()) {
                    hasSelectedCategory = true;
                    break;
                }
            }

            // If no category is selected, select all categories
            if (!hasSelectedCategory) {
                for (int i = 0; i < categoryListAdapter.getItemCount(); i++) {
                    JunkCategory category = categoryListAdapter.getCategoryList().get(i);
                    category.setSelected(true);
                    scanningViewModel.updateCategorySelection(i, true);
                }
                categoryListAdapter.notifyDataSetChanged();
            }

            // Execute cleanup operation
            executeCleanupProcess();
        });
    }

    /**
     * Execute the actual cleanup operation
     */
    private void executeCleanupProcess() {
        android.util.Log.d("CleanupScanActivity", "Starting cleanup process");

        // Check selected categories
        List<JunkCategory> selectedCategoryList = new ArrayList<>();
        for (JunkCategory category : categoryListAdapter.getCategoryList()) {
            if (category.isSelected()) {
                selectedCategoryList.add(category);
                android.util.Log.d("CleanupScanActivity", "Selected category: " + category.getName());
            }
        }

        if (selectedCategoryList.isEmpty()) {
            android.util.Log.w("CleanupScanActivity", "No categories selected for cleanup");
            android.widget.Toast.makeText(this, "Please select at least one category to clean", android.widget.Toast.LENGTH_SHORT).show();
            return;
        }

        // Show cleanup progress dialog
        android.app.AlertDialog progressDialog = showCleanupProgressDialog();

        // Use ViewModel to execute actual cleanup operation
        try {
            scanningViewModel.startCleaning();
            android.util.Log.d("CleanupScanActivity", "Called scanningViewModel.startCleaning()");
        } catch (Exception e) {
            android.util.Log.e("CleanupScanActivity", "Error starting cleaning process", e);
            progressDialog.dismiss();
            android.widget.Toast.makeText(this, "Error starting cleaning: " + e.getMessage(), android.widget.Toast.LENGTH_LONG).show();
            return;
        }

        // Observe cleanup progress
        scanningViewModel.getCleaningProgress().observe(this, progress -> {
            android.util.Log.d("CleanupScanActivity", "Cleaning progress update: " + progress + "%");

            // Update progress dialog
            updateCleanupProgressDialog(progressDialog, progress);

            // If cleanup is complete, navigate to completion page
            if (progress == 100) {
                android.util.Log.d("CleanupScanActivity", "Cleaning completed (100%)");
                uiHandler.postDelayed(() -> {
                    progressDialog.dismiss();
                    // Navigate to CleanupCompletedActivity
                    Intent intent = new Intent(this, CleanupCompletedActivity.class);
                    intent.putExtra("totalJunkSize", scanningViewModel.getTotalJunkSize().getValue());
                    startActivity(intent);
                    finish();
                }, 1000); // Delay 1 second to ensure user sees 100% progress
            }
        });

        // Observe cleanup path
        scanningViewModel.getCurrentCleaningPath().observe(this, path -> {
            if (path != null && !path.isEmpty()) {
                android.util.Log.d("CleanupScanActivity", "Current cleaning path: " + path);
            }

            // Update current cleanup path display in progress dialog
            updateCleanupPathInDialog(progressDialog, path);
        });

        // Observe error information
        scanningViewModel.getError().observe(this, error -> {
            if (error != null) {
                android.util.Log.e("CleanupScanActivity", "Error during cleaning: " + error);
                // Display error information
                android.widget.Toast.makeText(this, error, android.widget.Toast.LENGTH_LONG).show();
            }
        });

        // Observe cleanup status
        scanningViewModel.getIsCleaning().observe(this, isCleaning -> {
            android.util.Log.d("CleanupScanActivity", "Cleaning state changed: " + isCleaning);
            if (Boolean.FALSE.equals(isCleaning) && progressDialog.isShowing()) {
                // If cleanup stops but dialog is still showing, there might be an error
                Integer progress = scanningViewModel.getCleaningProgress().getValue();
                if (progress != null && progress < 100) {
                    android.util.Log.w("CleanupScanActivity", "Cleaning stopped before completion");
                    progressDialog.dismiss();
                    android.widget.Toast.makeText(this, "Cleaning stopped unexpectedly", android.widget.Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    /**
     * 显示清理进度对话框
     */
    private android.app.AlertDialog showCleanupProgressDialog() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        android.view.LayoutInflater inflater = getLayoutInflater();
        android.view.View dialogView = inflater.inflate(com.mobile.app.zencleaner.R.layout.dialog_progress, null);
        builder.setView(dialogView);
        builder.setCancelable(false);

        // 设置进度对话框的标题和消息
        TextView titleText = dialogView.findViewById(com.mobile.app.zencleaner.R.id.dialog_title);
        TextView messageText = dialogView.findViewById(com.mobile.app.zencleaner.R.id.dialog_message);

        // 隐藏原来的进度条，添加Lottie动画
        android.widget.ProgressBar progressBar = dialogView.findViewById(com.mobile.app.zencleaner.R.id.progress_bar);
        if (progressBar != null) {
            progressBar.setVisibility(View.GONE);
        }

        // 创建Lottie动画视图
        com.airbnb.lottie.LottieAnimationView animationView = new com.airbnb.lottie.LottieAnimationView(this);
        animationView.setAnimation(com.mobile.app.zencleaner.R.raw.clean);
        animationView.setRepeatCount(com.airbnb.lottie.LottieDrawable.INFINITE);
        animationView.playAnimation();

        // 设置动画视图的布局参数
        android.widget.LinearLayout.LayoutParams params = new android.widget.LinearLayout.LayoutParams(android.widget.LinearLayout.LayoutParams.MATCH_PARENT, (int) android.util.TypedValue.applyDimension(android.util.TypedValue.COMPLEX_UNIT_DIP, 200, getResources().getDisplayMetrics()));
        params.setMargins(0, 16, 0, 16);
        animationView.setLayoutParams(params);

        // 将动画视图添加到对话框中
        android.widget.LinearLayout container = (android.widget.LinearLayout) dialogView;
        container.addView(animationView, 1); // 在标题下方添加

        // 添加进度百分比文本视图
        TextView progressText = new TextView(this);
        progressText.setId(com.mobile.app.zencleaner.R.id.dialog_progress); // 使用预定义ID
        progressText.setTag("progress_text"); // 设置标签以便于查找
        progressText.setLayoutParams(new android.widget.LinearLayout.LayoutParams(android.widget.LinearLayout.LayoutParams.MATCH_PARENT, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT));
        progressText.setGravity(android.view.Gravity.CENTER);
        progressText.setTextSize(24); // 增大文本大小
        progressText.setTextColor(getResources().getColor(com.mobile.app.zencleaner.R.color.primary_blue));
        progressText.setText("0%");
        progressText.setTypeface(android.graphics.Typeface.DEFAULT_BOLD); // 设置为粗体
        container.addView(progressText, container.indexOfChild(messageText) + 1); // 在消息文本下方添加

        // 记录日志
        android.util.Log.d("JunkScanActivity", "Progress text view created with ID: " + progressText.getId());
        android.util.Log.d("JunkScanActivity", "Progress text view tag: " + progressText.getTag());

        titleText.setText("Cleaning");
        messageText.setText("Cleaning junk files...");

        android.app.AlertDialog dialog = builder.create();
        dialog.show();

        // 添加取消按钮
        android.widget.Button cancelButton = dialogView.findViewById(com.mobile.app.zencleaner.R.id.btn_cancel);
        if (cancelButton != null) {
//            cancelButton.setVisibility(View.VISIBLE);
            cancelButton.setOnClickListener(v -> {
                // 取消清理操作
                scanningViewModel.cancelCleaning();
                dialog.dismiss();
                android.widget.Toast.makeText(this, "Cleaning cancelled", android.widget.Toast.LENGTH_SHORT).show();
            });
        }

        return dialog;
    }

    /**
     * 更新清理进度对话框
     */
    private void updateCleanupProgressDialog(android.app.AlertDialog dialog, int progress) {
        if (dialog == null || !dialog.isShowing()) {
            return;
        }

        // 记录进度更新日志
        android.util.Log.d("JunkScanActivity", "Updating progress: " + progress + "%");

        // 查找进度文本视图 - 尝试多种方法
        // 方法1: 直接使用对话框查找
        TextView progressText = null;
        try {
            progressText = dialog.findViewById(com.mobile.app.zencleaner.R.id.dialog_progress);
        } catch (Exception e) {
            android.util.Log.e("JunkScanActivity", "Error finding progress text view (method 1): " + e.getMessage());
        }

        // 方法2: 使用对话框的内容视图
        if (progressText == null) {
            try {
                android.view.View contentView = dialog.findViewById(android.R.id.content);
                if (contentView != null) {
                    progressText = contentView.findViewById(com.mobile.app.zencleaner.R.id.dialog_progress);
                }
            } catch (Exception e) {
                android.util.Log.e("JunkScanActivity", "Error finding progress text view (method 2): " + e.getMessage());
            }
        }

        // 方法3: 使用对话框的根视图
        if (progressText == null) {
            try {
                android.view.View rootView = dialog.getWindow().getDecorView();
                progressText = findViewWithTag(rootView, "progress_text");
            } catch (Exception e) {
                android.util.Log.e("JunkScanActivity", "Error finding progress text view (method 3): " + e.getMessage());
            }
        }

        // 更新进度文本
        if (progressText != null) {
            progressText.setText(progress + "%");
            android.util.Log.d("JunkScanActivity", "Progress text updated to: " + progress + "%");
        } else {
            android.util.Log.e("JunkScanActivity", "Could not find progress text view!");
        }

        // 更新消息
        TextView messageText = null;
        try {
            messageText = dialog.findViewById(com.mobile.app.zencleaner.R.id.dialog_message);
        } catch (Exception e) {
            android.util.Log.e("JunkScanActivity", "Error finding message text view: " + e.getMessage());
        }

        if (messageText != null) {
            if (progress < 100) {
                messageText.setText("Cleaning junk files...");
            } else {
                messageText.setText("Cleaning completed!");
            }
        }

        // 如果进度为100%，停止动画循环
        if (progress >= 100) {
            // 查找Lottie动画视图
            try {
                android.view.View rootView = dialog.getWindow().getDecorView();
                com.airbnb.lottie.LottieAnimationView animationView = findLottieAnimationView(rootView);
                if (animationView != null) {
                    animationView.setRepeatCount(0); // 设置为不重复
                    android.util.Log.d("JunkScanActivity", "Animation set to not repeat");
                }
            } catch (Exception e) {
                android.util.Log.e("JunkScanActivity", "Error updating animation: " + e.getMessage());
            }
        }
    }

    /**
     * 递归查找具有指定标签的视图
     */
    private <T extends android.view.View> T findViewWithTag(android.view.View view, Object tag) {
        if (tag.equals(view.getTag())) {
            return (T) view;
        }

        if (view instanceof android.view.ViewGroup) {
            android.view.ViewGroup viewGroup = (android.view.ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                T result = findViewWithTag(viewGroup.getChildAt(i), tag);
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }

    /**
     * 递归查找LottieAnimationView
     */
    private com.airbnb.lottie.LottieAnimationView findLottieAnimationView(android.view.View view) {
        if (view instanceof com.airbnb.lottie.LottieAnimationView) {
            return (com.airbnb.lottie.LottieAnimationView) view;
        }

        if (view instanceof android.view.ViewGroup) {
            android.view.ViewGroup viewGroup = (android.view.ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                com.airbnb.lottie.LottieAnimationView result = findLottieAnimationView(viewGroup.getChildAt(i));
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }

    /**
     * 更新清理路径显示
     */
    private void updateCleanupPathInDialog(android.app.AlertDialog dialog, String path) {
        if (dialog == null || !dialog.isShowing() || path == null || path.isEmpty()) {
            return;
        }

        // 更新路径显示
        TextView pathText = dialog.findViewById(com.mobile.app.zencleaner.R.id.dialog_path);
        if (pathText != null) {
            // 截断过长的路径
            String displayPath = path;
            if (path.length() > 40) {
                int lastSeparator = path.lastIndexOf("/");
                if (lastSeparator > 0) {
                    displayPath = "..." + path.substring(lastSeparator);
                }
            }
            pathText.setText(displayPath);
            pathText.setVisibility(View.VISIBLE);
        }
    }

    private void prepareScanPaths() {
        // Add some sample paths to simulate scanning
        scanPathList.add("/storage/emulated/0/Download");
        scanPathList.add("/storage/emulated/0/DCIM/Camera");
        scanPathList.add("/storage/emulated/0/Android/data");
        scanPathList.add("/storage/emulated/0/WhatsApp");
        scanPathList.add("/storage/emulated/0/Pictures");
        scanPathList.add("/storage/emulated/0/Movies");
        scanPathList.add("/storage/emulated/0/Music");
        scanPathList.add("/storage/emulated/0/Documents");
    }

    private void startScanAnimation() {
        // 在开始扫描前清空已生成的类型集合
        usedCategoryTypes.clear();

        // Start the progress animation
        scanProgressAnimator = ValueAnimator.ofInt(0, 100);
        scanProgressAnimator.setDuration(8000); // 8 seconds for the full scan
        scanProgressAnimator.setInterpolator(new LinearInterpolator());

        // 预定义生成垃圾项的进度点，确保分布更均匀
        final int[] junkGenerationPoints = {15, 30, 45, 60, 80};

        // 初始化扫描状态
        binding.tvCurrentPath.setText("Initializing scan...");
        binding.tvScanPercentage.setText("0");

        scanProgressAnimator.addUpdateListener(animation -> {
            scanProgressValue = (int) animation.getAnimatedValue();
            binding.tvScanPercentage.setText(String.valueOf(scanProgressValue));

            // Update the current path being scanned
            if (scanProgressValue % 8 == 0) {
                updateCurrentPath();
            }

            // Generate junk items at predefined progress points
            for (int point : junkGenerationPoints) {
                if (scanProgressValue == point) {
                    generateJunkItem();
                    break;
                }
            }
        });

        scanProgressAnimator.start();

        // When animation completes, show the results
        uiHandler.postDelayed(this::showScanResults, 8000);
    }

    private void updateCurrentPath() {
        if (currentScanPathIndex < scanPathList.size()) {
            binding.tvCurrentPath.setText("Scanning: " + scanPathList.get(currentScanPathIndex));
            currentScanPathIndex = (currentScanPathIndex + 1) % scanPathList.size();
        }
    }

    private void generateJunkItem() {
        // Create a new junk category with random size
        JunkCategory category = createRandomJunkCategory();

        // 如果这个类型已经生成过，就不添加新的项目
        if (category != null) {
            // 检查当前列表中是否已经有相同类型的项目
            boolean isDuplicate = false;
            List<JunkCategory> currentList = categoryListAdapter.getCategoryList();

            for (JunkCategory existingItem : currentList) {
                if (existingItem.getName().equals(category.getName())) {
                    isDuplicate = true;
                    break;
                }
            }

            if (!isDuplicate) {
                // Add to adapter
                List<JunkCategory> newList = new ArrayList<>(currentList);
                newList.add(category);
                categoryListAdapter.refreshCategoryList(newList);

                // Add to total junk size
                totalDetectedJunkSize += category.getSizeBytes();
            }
        }
    }

    private JunkCategory createRandomJunkCategory() {
        String[] types = {"App Cache", "Temporary Files", "APK Files", "Empty Folders", "Log Files"};
        String[] descriptions = {"Cached data from applications", "Temporary files that can be safely removed", "Installation packages that are no longer needed", "Folders with no content", "System and app log files"};

        // 如果所有类型都已经生成过，则返回null
        if (usedCategoryTypes.size() >= types.length) {
            return null;
        }

        // 尝试找到一个未生成过的类型
        String selectedType = null;
        int typeIndex = -1;

        // 最多尝试types.length次，确保不会无限循环
        for (int i = 0; i < types.length; i++) {
            typeIndex = randomGenerator.nextInt(types.length);
            selectedType = types[typeIndex];

            if (!usedCategoryTypes.contains(selectedType)) {
                usedCategoryTypes.add(selectedType); // 标记为已生成
                break;
            }
        }

        // 如果没有找到新类型，返回null
        if (selectedType == null || typeIndex == -1) {
            return null;
        }

        // 生成随机大小
        long sizeBytes = randomGenerator.nextInt(50) * 1024 * 1024L + 1024 * 1024L; // 1-50 MB

        return new JunkCategory(selectedType, descriptions[typeIndex], sizeBytes, true);
    }

    private void showScanResults() {
        // 先停止扫描动画
        if (scanProgressAnimator != null && scanProgressAnimator.isRunning()) {
            scanProgressAnimator.cancel();
        }

        // 清理重复项
        removeDuplicateJunkItems();

        // Hide scanning view and show results
        binding.scanningView.setVisibility(View.GONE);
        binding.scanStatusHeader.setVisibility(View.VISIBLE);

        // Format and display the total junk size
        displayJunkSize(totalDetectedJunkSize);

        // Update scan status text based on total junk size
        if (totalDetectedJunkSize == 0) {
            binding.tvScanStatus.setText(R.string.scan_complete_no_significant_junk_found);
            // 如果没有垃圾，禁用下一步按钮
            binding.btnNextStep.setEnabled(false);
        } else {
            binding.tvScanStatus.setText(R.string.scan_complete_select_items_below_to_clean);
            // 启用下一步按钮
            binding.btnNextStep.setEnabled(true);
        }
    }

    /**
     * 显示垃圾大小，根据大小自动选择单位（KB/MB/GB）
     *
     * @param bytes 垃圾大小（字节）
     */
    private void displayJunkSize(long bytes) {
        if (bytes == 0) {
            // 如果没有垃圾，显示0 B
            binding.tvJunkSizeNumber.setText("0");
            binding.tvJunkSizeUnit.setText("B");
            return;
        }

        if (bytes < 1024 * 1024) { // Less than 1 MB
            float kb = bytes / 1024f;
            binding.tvJunkSizeNumber.setText(String.format("%.1f", kb));
            binding.tvJunkSizeUnit.setText("KB");
        } else if (bytes < 1024 * 1024 * 1024) { // Less than 1 GB
            float mb = bytes / (1024f * 1024f);
            binding.tvJunkSizeNumber.setText(String.format("%.1f", mb));
            binding.tvJunkSizeUnit.setText("MB");
        } else { // GB or larger
            float gb = bytes / (1024f * 1024f * 1024f);
            binding.tvJunkSizeNumber.setText(String.format("%.1f", gb));
            binding.tvJunkSizeUnit.setText("GB");
        }

        // 在数字较大时调整文本大小
        String sizeText = binding.tvJunkSizeNumber.getText().toString();
        if (sizeText.length() > 3) {
            binding.tvJunkSizeNumber.setTextSize(40); // 缩小文本大小
        } else {
            binding.tvJunkSizeNumber.setTextSize(48); // 恢复默认大小
        }
    }

    /**
     * 移除列表中的重复项
     */
    private void removeDuplicateJunkItems() {
        List<JunkCategory> currentItems = categoryListAdapter.getCategoryList();
        if (currentItems.isEmpty()) {
            return;
        }

        // 使用Set来记录已经出现过的类型
        Set<String> seenTypes = new HashSet<>();
        List<JunkCategory> uniqueItems = new ArrayList<>();

        // 重新计算总大小
        totalDetectedJunkSize = 0;

        for (JunkCategory item : currentItems) {
            String type = item.getName();
            if (!seenTypes.contains(type)) {
                seenTypes.add(type);
                uniqueItems.add(item);
                totalDetectedJunkSize += item.getSizeBytes();
            }
        }

        // 更新适配器
        categoryListAdapter.refreshCategoryList(uniqueItems);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (scanProgressAnimator != null) {
            scanProgressAnimator.cancel();
        }
        uiHandler.removeCallbacksAndMessages(null);

        // Clean up any ongoing animations in the adapter
        if (categoryListAdapter != null) {
            categoryListAdapter.cleanupAllAnimations();
        }
        binding = null;
    }
}
