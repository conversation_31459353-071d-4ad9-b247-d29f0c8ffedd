<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.ZenCleaner"
        tools:targetApi="31">

        <activity
            android:name=".ui.activities.LauncherActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.ZenCleaner">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activities.HomeActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.VideosActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.ImagesActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.CleanupScanActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.LargeFilesActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.CleanupCompletedActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.MoreActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.AgreementActivity"
            android:screenOrientation="portrait" />
    </application>

</manifest>