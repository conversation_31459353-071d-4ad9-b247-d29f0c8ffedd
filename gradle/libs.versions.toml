[versions]
accompanistPermissions = "0.37.2"
accompanistSystemuicontroller = "0.36.0"
agp = "8.9.1"
appcompat = "1.7.0"
coilCompose = "2.7.0"
compiler = "4.16.0"
constraintlayout = "2.2.1"
glide = "4.16.0"
kotlin = "2.0.21"
coreKtx = "1.15.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
lifecycleRuntimeKtx = "2.8.7"
activityCompose = "1.10.1"
composeBom = "2024.09.00"
datastoreCoreAndroid = "1.1.4"
androidxCore = "1.15.0"
androidxLifecycle = "2.8.7"
androidxActivity = "1.10.1"

# Compose BOM
# Compose Compiler 版本在 app/build.gradle.kts 中设置 (例如 1.5.10 或根据您的 Kotlin 版本调整)
# https://developer.android.com/jetpack/androidx/releases/compose-kotlin
androidxComposeBom = "2024.09.00" # 从您提供的版本更新

# Navigation Compose
androidxNavigation = "2.7.7" # 添加了缺失的版本

# DataStore
androidxDatastore = "1.1.1" # 添加了缺失的版本 (datastore-preferences 的)
androidxDatastoreCore = "1.1.1" # 您添加的 datastore-core 版本

# Coroutines
kotlinxCoroutines = "1.7.3" # 添加了缺失的版本 (建议检查最新稳定版)

# Testing
androidxTestJunit = "1.2.1" # 从您提供的版本更新 (junitVersion)
androidxEspresso = "3.6.1"
lottie = "6.6.6"
lottieCompose = "6.6.4"
material3Android = "1.3.1"
cardview = "1.0.0"
material = "1.12.0" # 从您提供的版本更新 (espressoCore)
materialVersion = "1.11.0"
navigationFragment = "2.9.0"
navigationUi = "2.9.0"
recyclerview = "1.4.0"

# Kotlin (通常在项目 build.gradle 或 settings.gradle 中定义，但放在这里也可以)
# kotlin = "2.0.21"

[libraries]
# AndroidX Core & Lifecycle & Activity
accompanist-permissions = { module = "com.google.accompanist:accompanist-permissions", version.ref = "accompanistPermissions" }
accompanist-systemuicontroller = { module = "com.google.accompanist:accompanist-systemuicontroller", version.ref = "accompanistSystemuicontroller" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "androidxCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "androidxLifecycle" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "androidxActivity" }
androidx-lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "androidxLifecycle" } # 添加了缺失的库
androidx-lifecycle-runtime-compose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "androidxLifecycle" } # 添加了缺失的库

# Compose (Versions managed by BOM)
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "androidxComposeBom" }
androidx-compose-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-compose-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" } # Debug dependency
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-compose-material = { group = "androidx.compose.material", name = "material" } # Material 2
androidx-compose-material-icons-core = { group = "androidx.compose.material", name = "material-icons-core" }
androidx-compose-material-icons-extended = { group = "androidx.compose.material", name = "material-icons-extended" }

# Navigation Compose
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "androidxNavigation" } # 使用了添加的版本引用

# DataStore
androidx-datastore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "androidxDatastore" } # 使用了添加的版本引用
androidx-datastore-core-android = { group = "androidx.datastore", name = "datastore-core-android", version.ref = "androidxDatastoreCore" } # 使用了您添加的版本

# Coroutines
androidx-navigation-fragment = { module = "androidx.navigation:navigation-fragment", version.ref = "navigationFragment" }
androidx-navigation-ui = { module = "androidx.navigation:navigation-ui", version.ref = "navigationUi" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
coil-compose = { module = "io.coil-kt:coil-compose", version.ref = "coilCompose" }
coil-video = { module = "io.coil-kt:coil-video", version.ref = "coilCompose" }
compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "compiler" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "kotlinxCoroutines"} # 使用了添加的版本引用

# Testing
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "androidxTestJunit" } # 使用了添加的版本引用
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "androidxEspresso" } # 使用了添加的版本引用
androidx-compose-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" } # Managed by BOM
androidx-compose-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-material3-android = { group = "androidx.compose.material3", name = "material3-android", version.ref = "material3Android" } # Debug dependency, managed by BOM
lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }
lottie-compose = { module = "com.airbnb.android:lottie-compose", version.ref = "lottieCompose" }
androidx-cardview = { group = "androidx.cardview", name = "cardview", version.ref = "cardview" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
material-v1110 = { module = "com.google.android.material:material", version.ref = "materialVersion" }
[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }

